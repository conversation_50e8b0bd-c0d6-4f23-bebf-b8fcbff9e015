# encoding:gbk
"""
价值平均+择时量化投资策略
基于创业板ETF(159915)技术指标进行择时，在沉睡期持有红利国企ETF(510720)，激活期持有创业板ETF(159915)
使用价值平均策略进行仓位管理
"""

import sqlite3
import datetime
import json
import math
import time
import numpy as np
from typing import Dict, List, Tuple, Optional
import traceback
import uuid

# ==================== 策略参数配置 ====================

#############################################################################
### 用户可以配置的参数 BEGIN
#############################################################################

# 基金代码配置
SLEEPING_FUND_CODE = "510720.SH"    # 沉睡期基金：国泰上证国有企业红利ETF
ACTIVE_FUND_CODE = "159967.SZ"      # 激活期基金：创业板ETF
SIGNAL_FUND_CODE = "159915.SZ"      # 信号检测基金：创业板ETF

# 价值平均策略参数
PERIOD_INVESTMENT_AMOUNT = 10000  # 每期投入金额（元）
INVESTMENT_CYCLE = "1mon"         # 投资周期：日(1d)、周(1w)、季(1q)、月(1mon)

# 技术指标参数(季线)
EMA_PERIOD = 35                 # EMA参数：默认35，可配置2-500
BOTTOM_RATIO = 0.85             # 底部相对比例：默认0.85
TOP_RATIO = 1.90                # 顶部相对比例：默认1.90

# 交易时点控制参数
TRADE_TIME_CONTROL = "144500"   # 交易时点控制：HHmmss格式，如144500代表14:45:00
ENABLE_TIME_CONTROL = True      # 是否启用时点控制（实盘模式有效，回测模式忽略， True代表只有在TRADE_TIME_CONTROL之后的时间才会执行买卖，False代表只要当天有K线就执行）

# 交易账户信息
ACCOUNT_ID = "************"     # 设置交易账户信息（只能支持一个）
ACCOUNT_TYPE = "CREDIT"         # 交易账户类型，手动设置
COMMISSION_FEE_RATE = 0.0001   # 佣金费率（万分之1）
COMMISSION_FEE_MIN = 5         # 最低交易佣金（元）
SELL_TAX_RATE = 0.0005          # 印花税率（万分之5，仅卖出）
TRANSFER_FEE_RATE = 0.00002    # 过户费率（万分之0.2，仅上海）

# START 强制激活期配置
FORCE_ACTIVE_MODE = True      # True: 强制进入激活期, False: 正常信号检测模式
AUTO_CALCULATE_ACTIVATION = True  # True: 自动计算最近的激活期买点, False: 使用手动设置的日期和价格
HISTORICAL_ANALYSIS_YEARS = 20     # 历史数据分析年数（用于自动计算激活点，建议3-5年）
FORCE_ACTIVE_START_DATE = "2024-06-28"  # 手动设置的起始日期（仅当AUTO_CALCULATE_ACTIVATION=False时使用）
FORCE_ACTIVE_START_PRICE = 1.234        # 手动设置的起始价格（仅当AUTO_CALCULATE_ACTIVATION=False时使用）
# END 强制激活期配置

#############################################################################
### 用户可以配置的参数 END
#############################################################################


#############################################################################
### 不推荐自行修改的参数 BEGIN
#############################################################################

# 技术指标参数
EMA_DETECTION_CYCLE = "1q"      # EMA检测周期：季(1q)

# 信号过滤参数
BUY_SIGNAL_FILTER_PERIODS = 8   # 买入信号过滤周期：8个周期内不重复
SELL_SIGNAL_FILTER_PERIODS = 10 # 卖出信号过滤周期：10个周期内不重复

# 交易执行参数
MAX_RETRY_COUNT = 3             # 最大重试次数
RETRY_INTERVAL = 300            # 重试间隔（秒）
MIN_TRADE_SHARES = 100          # 最小交易股数（必须是100的倍数）

# 数据库配置
DATABASE_PATH = "gytrading2.db"   # 数据库文件路径

# 回测模式配置
IS_BACKTEST_MODE = True        # True: 回测模式（不真实下单）, False: 实盘模式

#############################################################################
### 不推荐自行修改的参数 END
#############################################################################


# ==================== 平台兼容性检查 ====================
# iQuant平台函数可用性检查
IQUANT_FUNCTIONS_AVAILABLE = False
try:
    # 检查是否在iQuant环境中
    passorder
    get_trade_detail_data
    IQUANT_FUNCTIONS_AVAILABLE = True
    print("iQuant平台函数可用")
except NameError:
    print("非iQuant环境，将使用模拟函数")
    IQUANT_FUNCTIONS_AVAILABLE = False

    # 定义模拟函数
    def passorder(*args, **kwargs):
        """模拟passorder函数"""
        print(f"[模拟] passorder调用: args={args}")
        return "MOCK_ORDER_ID"

    def get_trade_detail_data(*args, **kwargs):
        """模拟get_trade_detail_data函数"""
        print(f"[模拟] get_trade_detail_data调用: args={args}")
        return {}

# ==================== 全局变量 ====================
g_strategy_status = None        # 策略状态
g_db_connection = None          # 数据库连接
g_current_bar_time = None       # 当前K线时间（回测时使用）


# ==================== 回测适配函数 ====================

def is_backtest_mode(ContextInfo=None) -> bool:
    """
    判断是否为回测模式（简单参数配置）

    Returns:
        bool: 是否为回测模式
    """
    return IS_BACKTEST_MODE


def get_current_time(ContextInfo) -> datetime.datetime:
    """
    获取当前时间（回测适配）

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        datetime.datetime: 当前时间
    """
    global g_current_bar_time

    try:
        if is_backtest_mode(ContextInfo):
            # 回测模式：使用当前K线的时间
            try:
                current_bar_timestamp = ContextInfo.get_bar_timetag(ContextInfo.barpos)
                if current_bar_timestamp:
                    # print(f'current_bar_ts ===> {current_bar_timestamp}')
                    g_current_bar_time = datetime.datetime.fromtimestamp(current_bar_timestamp/1000)
            except:
                print('获取K线时间失败，将会返回global的bartime')
                print(traceback.format_exc())
                pass
            
            # print(f'barpos = {ContextInfo.barpos}， bartime = {g_current_bar_time}')
            # 如果无法获取K线时间，使用缓存的时间
            if g_current_bar_time:
                return g_current_bar_time
            else:
                # 获取不到k线时间，也没有缓存时间，返回当前
                return datetime.datetime.now()
        else:
            # 实盘模式或无法获取K线时间：使用系统当前时间
            current_time = datetime.datetime.now()
            # 在实盘模式下也需要设置g_current_bar_time，供其他函数使用
            g_current_bar_time = current_time
            return current_time

    except Exception as e:
        log_message("WARNING", "时间获取", f"获取当前时间失败：{str(e)}", None, ContextInfo)
        current_time = datetime.datetime.now()
        # 异常情况下也需要设置g_current_bar_time
        g_current_bar_time = current_time
        return current_time


def get_current_time_str(ContextInfo) -> str:
    """
    获取当前时间字符串（回测适配）

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        str: 当前时间字符串，格式：%Y-%m-%d %H:%M:%S
    """
    return get_current_time(ContextInfo).strftime("%Y-%m-%d %H:%M:%S")


def should_execute_strategy(ContextInfo) -> bool:
    """
    判断是否应该执行策略逻辑（回测适配）
    如果是回测模式，每根K线都会执行；如果是实盘模式，则只会最后一根K线执行（is_last_bar）

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 是否应该执行策略
    """
    try:
        if is_backtest_mode(ContextInfo):
            # 回测模式：每根K线都执行（或根据需要调整频率）
            return True
        else:
            # 实盘模式：只在最后一根K线执行
            return ContextInfo.is_last_bar()

    except Exception as e:
        log_message("WARNING", "策略执行判断", f"判断是否执行策略失败：{str(e)}", None, ContextInfo)
        return False


def init(ContextInfo):
    """
    iQuant策略初始化函数
    在策略启动时调用一次，用于初始化数据库、加载策略状态等
    """
    try:
        print("=" * 60)
        print("价值平均+择时量化投资策略 - 初始化开始")
        print("=" * 60)

        ContextInfo.set_universe([ACTIVE_FUND_CODE, SLEEPING_FUND_CODE, SIGNAL_FUND_CODE])

        # 1. 初始化数据库
        print("正在初始化数据库...")
        init_database()
        print("数据库初始化完成！")

        # 2. 加载策略状态
        print("正在加载策略状态...")
        load_strategy_status()
        print("策略状态加载完成！")

        # 3. 验证策略状态
        print("正在验证策略状态...")
        if validate_strategy_state():
            print("策略状态验证通过！")
        else:
            print("策略状态验证失败，将重置状态！")
            reset_strategy_state()

        # 4. 检查强制激活模式
        if FORCE_ACTIVE_MODE:
            print("检测到强制激活模式，正在激活策略...")
            if force_activate_strategy(ContextInfo):
                print("策略强制激活成功！")
            else:
                print("策略强制激活失败！")

        # 5. 设置账户信息
        ContextInfo.set_account(ACCOUNT_ID)
        print(f"已设置交易账户:{ACCOUNT_ID}")

        # 6. 显示策略配置信息
        print("\n策略配置信息：")
        print(f"  沉睡期基金：{SLEEPING_FUND_CODE} (红利国企ETF)")
        print(f"  激活期基金：{ACTIVE_FUND_CODE} (创业板ETF)")
        print(f"  信号检测基金：{SIGNAL_FUND_CODE}")
        print(f"  每期投入金额：{PERIOD_INVESTMENT_AMOUNT:,}元")
        print(f"  投资周期：{INVESTMENT_CYCLE}")
        print(f"  EMA检测周期：{EMA_DETECTION_CYCLE}")
        print(f"  EMA参数：{EMA_PERIOD}")
        print(f"  底部比例：{BOTTOM_RATIO}")
        print(f"  顶部比例：{TOP_RATIO}")

        # 时点控制信息
        if ENABLE_TIME_CONTROL:
            trade_time_display = f"{TRADE_TIME_CONTROL[:2]}:{TRADE_TIME_CONTROL[2:4]}:{TRADE_TIME_CONTROL[4:6]}"
            print(f"  交易时点控制：启用，时点 {trade_time_display}")
        else:
            print(f"  交易时点控制：禁用")

        # 7. 显示当前策略状态
        print(f"\n当前策略状态：")
        print(f"  阶段：{g_strategy_status['current_phase']}")
        print(f"  强制激活模式：{'开启' if FORCE_ACTIVE_MODE else '关闭'}")
        if FORCE_ACTIVE_MODE:
            print(f"  自动计算激活点：{'开启' if AUTO_CALCULATE_ACTIVATION else '关闭'}")
            if AUTO_CALCULATE_ACTIVATION:
                print(f"  历史分析年数：{HISTORICAL_ANALYSIS_YEARS}年")
            else:
                print(f"  手动设置日期：{FORCE_ACTIVE_START_DATE}")
                print(f"  手动设置价格：{FORCE_ACTIVE_START_PRICE}")
        print(f"  最后检测时间：{g_strategy_status['last_check_time']}")
        if g_strategy_status['current_phase'] == 'active':
            print(f"  当前期数：{g_strategy_status['current_period']}")
            print(f"  起始期日期：{g_strategy_status['start_period_date']}")
            print(f"  起始期价格：{g_strategy_status['start_period_price']}")
            if g_strategy_status['first_activation_time']:
                print(f"  首次激活时间：{g_strategy_status['first_activation_time']}")

        # 8. 获取策略表现摘要
        performance = get_strategy_performance_summary()
        print(f"\n历史表现摘要：")
        print(f"  总交易次数：{performance['total_trades']}")
        print(f"  成功交易：{performance['successful_trades']}")
        print(f"  失败交易：{performance['failed_trades']}")
        print(f"  总信号数：{performance['total_signals']}")
        print(f"  有效信号：{performance['valid_signals']}")
        print(f"  过滤信号：{performance['filtered_signals']}")


        # 9. 每次启动策略都要执行一次数据下载
        current_date_str = datetime.datetime.now().strftime('%Y%m%d')
        print(f'即将下载 {SIGNAL_FUND_CODE} 的日线数据...')
        down_history_data(SIGNAL_FUND_CODE, '1d', '19900101', current_date_str)
        print(f'{SIGNAL_FUND_CODE} 的日线数据下载完成！')

        if ACTIVE_FUND_CODE != SIGNAL_FUND_CODE:
            print(f'即将下载 {ACTIVE_FUND_CODE} 的日线数据...')
            down_history_data(ACTIVE_FUND_CODE, '1d', '19900101', current_date_str)
            print(f'{ACTIVE_FUND_CODE} 的日线数据下载完成！')

        # 记录初始化日志
        log_message("INFO", "策略初始化", "策略初始化成功", None, ContextInfo)

        print("\n" + "=" * 60)
        print("价值平均+择时策略初始化完成！")
        print("=" * 60)

    except Exception as e:
        error_msg = f"策略初始化失败：{str(e)}"
        print(f"\n? {error_msg}")
        print("=" * 60)
        log_message("ERROR", "策略初始化", error_msg, None, ContextInfo)
        raise e  # 重新抛出异常，确保初始化失败时策略不会运行


def handlebar(ContextInfo):
    """
    iQuant主策略逻辑函数
    在每根K线结束时调用，执行核心策略逻辑
    """
    try:
        # print(f'\n\n\n###HANDLE_BAR### barpos = {ContextInfo.barpos}')
        # 判断是否应该执行策略逻辑（回测适配）
        if not should_execute_strategy(ContextInfo):
            return

        # 获取当前时间（回测适配）
        current_time = get_current_time_str(ContextInfo)

        if is_backtest_mode(ContextInfo):
            print(f"[回测模式] 执行策略，K线时间：{current_time}")
        else:
            print(f"[实盘模式] 当前为最后一根K线，执行策略，时间：{current_time}")
        
        # 验证策略状态
        if not validate_strategy_state():
            log_message("ERROR", "策略运行", "策略状态验证失败，跳过本次执行", None, ContextInfo)
            return

        # 检查数据库连接
        if g_db_connection is None:
            log_message("ERROR", "策略运行", "数据库连接丢失，跳过本次执行", None, ContextInfo)
            return

        # 检查账户是否已经登录
        account_data = get_account_info(ContextInfo)
        if not account_data or len(account_data) <= 0:
            log_message("ERROR", "策略运行", "当前账户未登录，请先登录！", None, ContextInfo)
            return

        # 2. 执行信号检测
        try:
            signal_result = detect_signals(ContextInfo)
            # print('执行信号检测==>')
            # print(signal_result)
            if signal_result is None:
                log_message("WARNING", "策略运行", "信号检测失败，跳过本次执行", None, ContextInfo)
                return
        except Exception as e:
            log_message("ERROR", "策略运行", f"信号检测异常：{str(e)}", None, ContextInfo)
            return

        # 3. 记录信号检测结果
        if signal_result.get('has_buy_signal'):
            log_message("INFO", "策略运行", "检测到买入信号", None, ContextInfo)
        if signal_result.get('has_sell_signal'):
            log_message("INFO", "策略运行", "检测到卖出信号", None, ContextInfo)

        # 4. 执行交易逻辑（带防重复交易机制）
        try:
            execute_trading_logic(ContextInfo, signal_result)
        except Exception as e:
            log_message("ERROR", "策略运行", f"交易逻辑执行异常：{str(e)}", None, ContextInfo)
            # 交易失败不应该中断策略运行，继续执行状态更新

        # 5. 处理交易任务队列
        try:
            task_queue_process_pending_tasks(ContextInfo)
        except Exception as e:
            log_message("ERROR", "策略运行", f"任务队列处理异常：{str(e)}", None, ContextInfo)

        # 5.5. 检查是否需要更新账户信息（基于任务日志）
        try:
            update_account_info_if_needed(ContextInfo)
        except Exception as e:
            log_message("ERROR", "策略运行", f"账户信息更新异常：{str(e)}", None, ContextInfo)

        # 6. 更新策略状态
        try:
            update_strategy_status(current_time)
        except Exception as e:
            log_message("ERROR", "策略运行", f"策略状态更新异常：{str(e)}", None, ContextInfo)

        # 6. 定期输出策略状态（每10次执行输出一次详细信息）
        if hasattr(ContextInfo, 'run_count'):
            ContextInfo.run_count += 1
        else:
            ContextInfo.run_count = 1

        if ContextInfo.run_count % 10 == 0:
            performance = get_strategy_performance_summary()
            log_message("INFO", "策略运行",
                       f"策略运行统计 - 阶段：{performance['current_phase']}, "
                       f"交易：{performance['successful_trades']}/{performance['total_trades']}, "
                       f"信号：{performance['valid_signals']}/{performance['total_signals']}", None, ContextInfo)

        # 7. 记录运行日志
        log_message("INFO", "策略运行",
                   f"策略执行完成，当前状态：{g_strategy_status['current_phase']}, "
                   f"运行次数：{ContextInfo.run_count}", None, ContextInfo)

    except Exception as e:
        error_msg = f"策略执行失败：{str(e)}"
        print(f"? {error_msg}")
        log_message("ERROR", "策略执行", error_msg, None, ContextInfo)

        # 记录跳过周期
        try:
            record_skip_period(error_msg)
        except:
            pass  # 避免二次异常


def update_account_info_if_needed(ContextInfo):
    """
    检查是否需要更新账户信息，如果有交易完成则更新

    Args:
        ContextInfo: iQuant上下文信息对象
    """
    try:
        cursor = g_db_connection.cursor()

        # 查找需要更新账户信息的任务日志
        cursor.execute("""
            SELECT COUNT(*) FROM trade_task_log
            WHERE log_category = 'ACCOUNT_UPDATE_NEEDED'
            AND log_time > datetime('now', '-1 hour')
        """)

        update_needed_count = cursor.fetchone()[0]

        if update_needed_count > 0:

            # 获取真实账户信息
            account_info = get_account_info(ContextInfo)
            if account_info:
                # 记录到数据库
                record_account_info(account_info)

                # 清理已处理的更新需求日志
                cursor.execute("""
                    DELETE FROM trade_task_log
                    WHERE log_category = 'ACCOUNT_UPDATE_NEEDED'
                    AND log_time <= datetime('now')
                """)
                g_db_connection.commit()

    except Exception as e:
        import traceback
        traceback.print_exc()


def record_skip_period(reason: str):
    """
    记录跳过的周期

    Args:
        reason: 跳过原因
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 获取当前期数
        current_period = g_strategy_status.get('current_period', 0) if g_strategy_status else 0

        cursor.execute("""
            INSERT INTO skip_periods
            (skip_date, period_number, target_amount, available_funds,
             required_funds, skip_reason, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            current_time, current_period, 0, 0, 0, reason, current_time
        ))

        g_db_connection.commit()

    except Exception as e:
        print(f"记录跳过周期失败：{str(e)}")


# ==================== 异常处理和重试机制 ====================

def retry_on_failure(func, max_retries: int = MAX_RETRY_COUNT, delay: int = RETRY_INTERVAL):
    """
    通用重试装饰器函数

    Args:
        func: 要重试的函数
        max_retries: 最大重试次数
        delay: 重试间隔（秒）

    Returns:
        函数执行结果
    """
    def wrapper(*args, **kwargs):
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    log_message("WARNING", "重试机制",
                               f"函数{func.__name__}执行失败，第{attempt + 1}次重试：{str(e)}")
                    time.sleep(delay)
                else:
                    log_message("ERROR", "重试机制",
                               f"函数{func.__name__}重试{max_retries}次后仍然失败：{str(e)}")

        # 所有重试都失败，抛出最后一个异常
        raise last_exception

    return wrapper


def safe_execute(func, *args, default_return=None, log_prefix="", **kwargs):
    """
    安全执行函数，捕获异常并返回默认值

    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 异常时的默认返回值
        log_prefix: 日志前缀
        **kwargs: 函数关键字参数

    Returns:
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_msg = f"{log_prefix}执行失败：{str(e)}"
        log_message("ERROR", "安全执行", error_msg, None)
        return default_return


def validate_trading_environment(ContextInfo) -> bool:
    """
    验证交易环境是否正常

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 环境是否正常
    """
    try:
        # 检查数据库连接
        if g_db_connection is None:
            log_message("ERROR", "环境验证", "数据库连接为空", None, ContextInfo)
            return False

        # 检查策略状态
        if g_strategy_status is None:
            log_message("ERROR", "环境验证", "策略状态为空", None, ContextInfo)
            return False

        # 检查账户ID
        if not ACCOUNT_ID:
            log_message("ERROR", "环境验证", "账户ID未设置", None, ContextInfo)
            return False

        # 检查是否为交易时间（简化检查）
        current_time = datetime.datetime.now()
        if current_time.weekday() >= 5:  # 周末
            log_message("WARNING", "环境验证", "当前为周末，非交易时间", None, ContextInfo)
            return False

        # 检查基本的市场数据获取能力
        try:
            test_data = ContextInfo.get_market_data_ex(
                fields=['close'],
                stock_code=[SIGNAL_FUND_CODE],
                period='1min',
                count=1,
                dividend_type='front',
                fill_data=True
            )
            if test_data is None or len(test_data) == 0:
                log_message("WARNING", "环境验证", "无法获取市场数据", None, ContextInfo)
                return False

            # 检查是否有具体股票的数据
            stock_data = test_data.get(SIGNAL_FUND_CODE)
            if stock_data is None or len(stock_data) == 0:
                log_message("WARNING", "环境验证", "无法获取市场数据", None, ContextInfo)
                return False
        except Exception as e:
            log_message("ERROR", "环境验证", f"市场数据获取测试失败：{str(e)}", None, ContextInfo)
            return False

        return True

    except Exception as e:
        log_message("ERROR", "环境验证", f"交易环境验证失败：{str(e)}", None, ContextInfo)
        return False


def handle_critical_error(error_msg: str, ContextInfo=None):
    """
    处理关键错误

    Args:
        error_msg: 错误消息
        ContextInfo: iQuant上下文信息对象
    """
    try:
        # 记录关键错误
        log_message("CRITICAL", "关键错误", error_msg, None, ContextInfo)

        # 发送错误通知（如果有通知机制）
        print(f"?? 关键错误：{error_msg}")

        # 保存当前状态
        if g_strategy_status and g_db_connection:
            try:
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                update_strategy_status(current_time)
            except:
                pass

        # 记录错误到跳过周期表
        try:
            record_skip_period(f"关键错误：{error_msg}")
        except:
            pass

    except Exception as e:
        print(f"处理关键错误时发生异常：{str(e)}")


def cleanup_resources():
    """
    清理资源
    在策略停止时调用
    """
    try:
        global g_db_connection

        if g_db_connection:
            # 记录策略停止日志
            log_message("INFO", "资源清理", "策略停止，开始清理资源", None)

            # 关闭数据库连接
            g_db_connection.close()
            g_db_connection = None

            print("? 资源清理完成")

    except Exception as e:
        print(f"资源清理失败：{str(e)}")


def get_error_statistics() -> Dict:
    """
    获取错误统计信息

    Returns:
        dict: 错误统计
    """
    try:
        stats = {
            'total_errors': 0,
            'critical_errors': 0,
            'warning_count': 0,
            'retry_count': 0,
            'skip_periods': 0
        }

        if g_db_connection is None:
            return stats

        cursor = g_db_connection.cursor()

        # 统计日志中的错误
        cursor.execute("SELECT COUNT(*) FROM trade_logs WHERE log_type = 'ERROR'")
        result = cursor.fetchone()
        if result:
            stats['total_errors'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM trade_logs WHERE log_type = 'CRITICAL'")
        result = cursor.fetchone()
        if result:
            stats['critical_errors'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM trade_logs WHERE log_type = 'WARNING'")
        result = cursor.fetchone()
        if result:
            stats['warning_count'] = result[0]

        # 统计重试次数
        cursor.execute("SELECT SUM(retry_count) FROM trade_orders WHERE retry_count > 0")
        result = cursor.fetchone()
        if result and result[0]:
            stats['retry_count'] = result[0]

        # 统计跳过周期
        cursor.execute("SELECT COUNT(*) FROM skip_periods")
        result = cursor.fetchone()
        if result:
            stats['skip_periods'] = result[0]

        return stats

    except Exception as e:
        log_message("ERROR", "错误统计", f"获取错误统计失败：{str(e)}", None)
        return {
            'total_errors': 0,
            'critical_errors': 0,
            'warning_count': 0,
            'retry_count': 0,
            'skip_periods': 0
        }


def init_database():
    """
    初始化SQLite数据库
    创建所有必要的表结构和索引
    """
    global g_db_connection

    try:
        # 连接数据库
        g_db_connection = sqlite3.connect(DATABASE_PATH)
        cursor = g_db_connection.cursor()

        # 创建策略状态表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                current_phase TEXT NOT NULL,           -- 当前阶段：'sleeping' 或 'active'
                last_check_time TEXT NOT NULL,         -- 最后检测时间
                first_activation_time TEXT,            -- 首次激活时间
                start_period_date TEXT,                -- 价值平均起始期日期（最高点日期）
                start_period_price REAL,              -- 起始期价格
                current_period INTEGER DEFAULT 0,      -- 当前期数
                last_adjustment_period INTEGER DEFAULT 0, -- 最后调整的期数（防止重复调整）
                last_adjustment_time TEXT,             -- 最后调整时间
                created_time TEXT NOT NULL,           -- 创建时间
                updated_time TEXT NOT NULL            -- 更新时间
            )
        """)

        # 创建信号历史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS signal_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                signal_date TEXT NOT NULL,            -- 信号日期
                signal_type TEXT NOT NULL,            -- 信号类型：'ENTERLONG' 或 'EXITLONG'
                signal_price REAL NOT NULL,           -- 信号价格
                ema_value REAL NOT NULL,              -- EMA值
                bottom_line REAL,                     -- 底部线F1值（买入信号时）
                top_line REAL,                        -- 顶部线F2值（卖出信号时）
                kline_position INTEGER,               -- K线位置（barpos）
                kline_date TEXT,                      -- K线日期（YYYYMMDD格式）
                is_valid INTEGER NOT NULL,            -- 是否有效信号：1有效，0无效
                filter_reason TEXT,                   -- 过滤原因
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建交易指令表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_uuid TEXT,                      -- 订单UUID，和异步任务对应起来
                order_date TEXT NOT NULL,             -- 下单日期
                stock_code TEXT NOT NULL,             -- 股票代码
                order_type TEXT NOT NULL,             -- 订单类型：'BUY' 或 'SELL'
                order_reason TEXT NOT NULL,           -- 下单原因：'SIGNAL_BUY', 'SIGNAL_SELL', 'VALUE_AVERAGE'
                target_amount REAL,                   -- 目标金额
                target_shares INTEGER,                -- 目标股数
                actual_shares INTEGER,                -- 实际成交股数
                actual_price REAL,                    -- 实际成交价格
                order_status TEXT NOT NULL,           -- 订单状态：'PENDING', 'SUCCESS', 'FAILED', 'RETRY'
                retry_count INTEGER DEFAULT 0,        -- 重试次数
                error_message TEXT,                   -- 错误信息
                execution_time TEXT,                  -- 执行时间
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建持仓记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS position_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                record_date TEXT NOT NULL,            -- 记录日期
                stock_code TEXT NOT NULL,             -- 股票代码
                shares INTEGER NOT NULL,              -- 持仓股数
                avg_cost REAL NOT NULL,               -- 平均成本
                market_value REAL NOT NULL,           -- 市值
                current_price REAL NOT NULL,          -- 当前价格
                period_number INTEGER,                -- 期数（仅159915有效）
                target_value REAL,                    -- 目标价值（仅159915有效）
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建跳过周期表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS skip_periods (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                skip_date TEXT NOT NULL,              -- 跳过日期
                period_number INTEGER NOT NULL,       -- 跳过的期数
                target_amount REAL NOT NULL,          -- 目标金额
                available_funds REAL NOT NULL,        -- 可用资金
                required_funds REAL NOT NULL,         -- 需要资金
                skip_reason TEXT NOT NULL,            -- 跳过原因
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建交易执行日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_date TEXT NOT NULL,               -- 日志日期（系统时间）
                kline_date TEXT,                      -- K线日期（回测时使用）
                log_type TEXT NOT NULL,               -- 日志类型：'INFO', 'WARNING', 'ERROR'
                operation TEXT NOT NULL,              -- 操作类型
                message TEXT NOT NULL,                -- 日志消息
                details TEXT,                         -- 详细信息（JSON格式）
                is_backtest INTEGER DEFAULT 0,       -- 是否回测模式（0=实盘，1=回测）
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建账户信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id TEXT NOT NULL UNIQUE,      -- 账户ID（唯一）
                total_assets REAL NOT NULL,           -- 总资产
                available_cash REAL NOT NULL,         -- 可用现金
                credit_limit REAL,                    -- 融资额度
                credit_available REAL,                -- 可用融资
                update_time TEXT NOT NULL,            -- 更新时间
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建交易任务队列表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_task_queue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_group_id TEXT NOT NULL,          -- 任务组ID(UUID)
                task_type TEXT NOT NULL,              -- 任务类型: SELL_510720, BUY_159915_CASH, BUY_159915_MARGIN
                stock_code TEXT NOT NULL,             -- 股票代码
                target_shares INTEGER NOT NULL,       -- 目标股数
                target_amount REAL,                   -- 目标金额
                estimated_price REAL,                 -- 预估价格
                estimated_fees REAL,                  -- 预估费用
                task_status TEXT NOT NULL,            -- PENDING, EXECUTING, WAITING_CALLBACK, COMPLETED, FAILED, TIMEOUT
                depends_on_task TEXT,                 -- 依赖的任务ID
                order_id TEXT,                        -- 实际下单后的订单ID
                order_uuid TEXT,                      -- 订单UUID（用于回调匹配）
                task_params TEXT,                     -- 任务参数JSON
                created_time TEXT NOT NULL,           -- 创建时间
                started_time TEXT,                    -- 开始执行时间
                completed_time TEXT,                  -- 完成时间
                error_message TEXT,                   -- 错误信息
                warning_logged INTEGER DEFAULT 0,     -- 是否已记录警告
                status_queried INTEGER DEFAULT 0,     -- 是否已查询状态
                alert_sent INTEGER DEFAULT 0          -- 是否已发送告警
            )
        """)



        # 创建交易任务日志表（合并了原 trade_task_execution 表的功能）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_task_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id INTEGER,                      -- 关联trade_task_queue.id，可为空
                task_group_id TEXT,                   -- 任务组ID，用于关联整个任务组的日志
                log_level TEXT NOT NULL,              -- 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
                log_category TEXT NOT NULL,           -- 日志分类: TASK_CREATE, TASK_EXECUTE, CALLBACK, TIMEOUT, EXECUTION_STEP, etc.
                log_message TEXT NOT NULL,            -- 日志消息
                extra_data TEXT,                      -- 额外数据JSON
                log_time TEXT NOT NULL,               -- 日志时间
                -- 以下字段来自原 trade_task_execution 表，用于记录执行数据
                execution_step TEXT,                  -- 执行步骤（如 ORDER_CALLBACK, EXECUTION_RESULT）
                step_status TEXT,                     -- 步骤状态（如 RECEIVED, COMPLETED）
                actual_shares INTEGER,                -- 实际股数
                actual_price REAL,                    -- 实际价格
                actual_amount REAL,                   -- 实际金额
                actual_fees REAL,                     -- 实际费用
                callback_data TEXT,                   -- 回调数据JSON
                FOREIGN KEY (task_id) REFERENCES trade_task_queue (id)
            )
        """)

        # 创建账户快照表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_snapshot (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_group_id TEXT NOT NULL,          -- 任务组ID
                snapshot_point TEXT NOT NULL,         -- 快照时点: BEFORE_SELL, AFTER_SELL, AFTER_BUY_CASH, AFTER_BUY_MARGIN
                available_cash REAL,                  -- 可用现金
                margin_available REAL,                -- 融资可用额度
                stock_510720_shares INTEGER,          -- 510720持股
                stock_510720_value REAL,              -- 510720市值
                stock_159915_shares INTEGER,          -- 159915持股
                stock_159915_value REAL,              -- 159915市值
                snapshot_time TEXT NOT NULL           -- 快照时间
            )
        """)

        # 创建交易执行记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_execution_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                trade_time TEXT NOT NULL,             -- 交易时间
                trade_type TEXT NOT NULL,             -- 交易类型：BUY/SELL
                stock_code TEXT NOT NULL,             -- 股票代码
                shares INTEGER NOT NULL,              -- 股数
                price REAL,                           -- 价格
                amount REAL,                          -- 金额
                fees REAL,                            -- 总费用
                order_id TEXT,                        -- 订单ID
                order_uuid TEXT,                      -- 订单UUID（用于回调匹配）
                status TEXT NOT NULL,                 -- 状态：SUCCESS/FAILED/PENDING/CANCELLED
                error_message TEXT,                   -- 错误信息
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建交易费用明细表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_fee_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                execution_log_id INTEGER,            -- 关联trade_execution_log.id
                order_uuid TEXT NOT NULL,            -- 订单UUID
                commission REAL DEFAULT 0,           -- 佣金
                stamp_tax REAL DEFAULT 0,            -- 印花税
                transfer_fee REAL DEFAULT 0,         -- 过户费
                other_fees REAL DEFAULT 0,           -- 其他费用
                total_fees REAL DEFAULT 0,           -- 总费用
                net_amount REAL DEFAULT 0,           -- 净金额
                created_time TEXT NOT NULL,          -- 创建时间
                FOREIGN KEY (execution_log_id) REFERENCES trade_execution_log (id)
            )
        """)

        # 创建订单状态历史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS order_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_uuid TEXT NOT NULL,            -- 订单UUID
                order_id TEXT,                       -- 订单ID
                stock_code TEXT,                     -- 股票代码
                order_status INTEGER NOT NULL,       -- 订单状态码
                status_desc TEXT,                    -- 状态描述
                volume_traded INTEGER DEFAULT 0,    -- 已成交量
                volume_total INTEGER DEFAULT 0,     -- 总委托量
                callback_time TEXT NOT NULL,        -- 回调时间
                created_time TEXT NOT NULL          -- 记录创建时间
            )
        """)

        # 创建索引
        create_database_indexes(cursor)

        # 检查是否需要插入初始策略状态
        cursor.execute("SELECT COUNT(*) FROM strategy_status")
        if cursor.fetchone()[0] == 0:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            cursor.execute("""
                INSERT INTO strategy_status
                (current_phase, last_check_time, created_time, updated_time)
                VALUES ('sleeping', ?, ?, ?)
            """, (current_time, current_time, current_time))

        g_db_connection.commit()
        print("数据库初始化完成")


    except Exception as e:
        error_msg = f"数据库初始化失败：{str(e)}"
        print(error_msg)
        raise e


def create_database_indexes(cursor):
    """
    创建数据库索引
    提高查询性能
    """
    try:
        # 信号历史表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_signal_history_date ON signal_history(signal_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_signal_history_type ON signal_history(signal_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_signal_history_kline_pos ON signal_history(kline_position)")

        # 交易指令表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_orders_date ON trade_orders(order_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_orders_status ON trade_orders(order_status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_orders_code ON trade_orders(stock_code)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_orders_uuid ON trade_orders(order_uuid)")

        # 持仓记录表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_position_records_date ON position_records(record_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_position_records_code ON position_records(stock_code)")

        # 跳过周期表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_skip_periods_date ON skip_periods(skip_date)")

        # 交易日志表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_logs_date ON trade_logs(log_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_logs_type ON trade_logs(log_type)")

        # 交易任务队列表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_queue_group_id ON trade_task_queue(task_group_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_queue_status ON trade_task_queue(task_status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_queue_type ON trade_task_queue(task_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_queue_order_id ON trade_task_queue(order_id)")

        # 交易任务日志表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_log_task_id ON trade_task_log(task_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_log_group_id ON trade_task_log(task_group_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_log_time ON trade_task_log(log_time)")

        # 账户快照表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_account_snapshot_group_id ON account_snapshot(task_group_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_account_snapshot_point ON account_snapshot(snapshot_point)")

        # 交易执行记录表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_execution_log_time ON trade_execution_log(trade_time)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_execution_log_uuid ON trade_execution_log(order_uuid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_execution_log_status ON trade_execution_log(status)")

        # 交易费用明细表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_fee_details_uuid ON trade_fee_details(order_uuid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_fee_details_execution_id ON trade_fee_details(execution_log_id)")

        # 订单状态历史表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_order_status_history_uuid ON order_status_history(order_uuid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_order_status_history_time ON order_status_history(callback_time)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_order_status_history_status ON order_status_history(order_status)")

        # 任务队列表新增UUID索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_queue_uuid ON trade_task_queue(order_uuid)")

    except Exception as e:
        print(f"创建索引失败：{str(e)}")


def load_strategy_status():
    """
    加载策略状态
    从数据库中读取当前策略状态信息
    """
    global g_strategy_status

    try:
        cursor = g_db_connection.cursor()
        cursor.execute("SELECT * FROM strategy_status ORDER BY id DESC LIMIT 1")
        result = cursor.fetchone()

        if result:
            g_strategy_status = {
                'id': result[0],
                'current_phase': result[1],
                'last_check_time': result[2],
                'first_activation_time': result[3],
                'start_period_date': result[4],
                'start_period_price': result[5],
                'current_period': result[6],
                'created_time': result[7],
                'updated_time': result[8]
            }
        else:
            # 如果没有记录，创建默认状态
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            g_strategy_status = {
                'id': None,
                'current_phase': 'sleeping',
                'last_check_time': current_time,
                'first_activation_time': None,
                'start_period_date': None,
                'start_period_price': None,
                'current_period': 0,
                'created_time': current_time,
                'updated_time': current_time
            }

    except Exception as e:
        error_msg = f"加载策略状态失败：{str(e)}"
        print(error_msg)
        raise e


def log_message(log_type: str, operation: str, message: str, details: Dict = None, ContextInfo=None):
    """
    记录日志消息到数据库
    支持回测模式下记录K线日期（向后兼容）

    Args:
        log_type: 日志类型 ('INFO', 'WARNING', 'ERROR')
        operation: 操作类型
        message: 日志消息
        details: 详细信息字典（可选）
        ContextInfo: iQuant上下文信息对象（可选，用于获取K线时间）
    """
    try:
        if g_db_connection is None:
            print(f"[{log_type}] {operation}: {message}")
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        details_json = json.dumps(details, ensure_ascii=False) if details else None

        # 获取K线日期和回测模式标识
        kline_date = current_time  # 默认使用系统时间
        is_backtest = 0

        if ContextInfo:
            try:
                is_backtest = 1 if is_backtest_mode(ContextInfo) else 0
                if is_backtest:
                    # 回测模式：使用当前K线时间
                    kline_time = get_current_time(ContextInfo)
                    kline_date = kline_time.strftime("%Y-%m-%d %H:%M:%S")
                # 实盘模式：kline_date已经设置为current_time
            except Exception as e:
                print(f"获取K线时间失败：{str(e)}")
                # 出错时使用系统时间，保持向后兼容

        cursor.execute("""
            INSERT INTO trade_logs (log_date, kline_date, log_type, operation, message, details, is_backtest, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, kline_date, log_type, operation, message, details_json, is_backtest, current_time))

        g_db_connection.commit()

    except Exception as e:
        print(f"记录日志失败：{str(e)}")


def query_trade_logs_by_kline_date(start_date: str = None, end_date: str = None, operation: str = None, log_type: str = None):
    """
    按K线日期查询交易日志

    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        operation: 操作类型过滤
        log_type: 日志类型过滤

    Returns:
        list: 查询结果
    """
    try:
        if g_db_connection is None:
            print("数据库连接为空")
            return []

        cursor = g_db_connection.cursor()

        # 构建查询条件
        conditions = []
        params = []

        if start_date:
            conditions.append("DATE(kline_date) >= ?")
            params.append(start_date)

        if end_date:
            conditions.append("DATE(kline_date) <= ?")
            params.append(end_date)

        if operation:
            conditions.append("operation = ?")
            params.append(operation)

        if log_type:
            conditions.append("log_type = ?")
            params.append(log_type)

        where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""

        query = f"""
            SELECT
                id,
                log_date,
                kline_date,
                log_type,
                operation,
                message,
                details,
                is_backtest,
                created_time
            FROM trade_logs
            {where_clause}
            ORDER BY kline_date DESC, id DESC
        """

        cursor.execute(query, params)
        results = cursor.fetchall()

        # 转换为字典格式便于查看
        columns = ['id', 'log_date', 'kline_date', 'log_type', 'operation', 'message', 'details', 'is_backtest', 'created_time']
        return [dict(zip(columns, row)) for row in results]

    except Exception as e:
        print(f"查询交易日志失败：{str(e)}")
        return []


def print_trade_logs_summary(start_date: str = None, end_date: str = None):
    """
    打印交易日志摘要

    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    """
    try:
        logs = query_trade_logs_by_kline_date(start_date, end_date)

        if not logs:
            print("没有找到匹配的日志记录")
            return

        print(f"\n=== 交易日志摘要 ({len(logs)}条记录) ===")

        # 按K线日期分组
        from collections import defaultdict
        logs_by_date = defaultdict(list)

        for log in logs:
            kline_date = log['kline_date']
            if kline_date:
                date_part = kline_date.split(' ')[0]  # 只取日期部分
                logs_by_date[date_part].append(log)

        # 按日期显示
        for date in sorted(logs_by_date.keys(), reverse=True):
            date_logs = logs_by_date[date]
            backtest_count = sum(1 for log in date_logs if log['is_backtest'])

            print(f"\n📅 {date} ({'回测' if backtest_count > 0 else '实盘'}) - {len(date_logs)}条记录")

            # 按操作类型统计
            operations = defaultdict(int)
            for log in date_logs:
                operations[log['operation']] += 1

            for op, count in operations.items():
                print(f"   {op}: {count}条")

    except Exception as e:
        print(f"打印日志摘要失败：{str(e)}")


def update_strategy_status(current_time: str):
    """
    更新策略状态到数据库

    Args:
        current_time: 当前时间字符串
    """
    try:
        if g_strategy_status is None:
            return

        cursor = g_db_connection.cursor()
        g_strategy_status['last_check_time'] = current_time
        g_strategy_status['updated_time'] = current_time

        if g_strategy_status['id'] is None:
            # 插入新记录
            cursor.execute("""
                INSERT INTO strategy_status
                (current_phase, last_check_time, first_activation_time, start_period_date,
                 start_period_price, current_period, created_time, updated_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                g_strategy_status['current_phase'],
                g_strategy_status['last_check_time'],
                g_strategy_status['first_activation_time'],
                g_strategy_status['start_period_date'],
                g_strategy_status['start_period_price'],
                g_strategy_status['current_period'],
                g_strategy_status['created_time'],
                g_strategy_status['updated_time']
            ))
            g_strategy_status['id'] = cursor.lastrowid
        else:
            # 更新现有记录
            cursor.execute("""
                UPDATE strategy_status SET
                current_phase = ?, last_check_time = ?, first_activation_time = ?,
                start_period_date = ?, start_period_price = ?, current_period = ?,
                updated_time = ?
                WHERE id = ?
            """, (
                g_strategy_status['current_phase'],
                g_strategy_status['last_check_time'],
                g_strategy_status['first_activation_time'],
                g_strategy_status['start_period_date'],
                g_strategy_status['start_period_price'],
                g_strategy_status['current_period'],
                g_strategy_status['updated_time'],
                g_strategy_status['id']
            ))

        g_db_connection.commit()

    except Exception as e:
        error_msg = f"更新策略状态失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "策略状态更新", error_msg, None)


def update_technical_indicators(ContextInfo):
    """
    更新技术指标
    获取159915的季线数据，计算EMA指标，同时返回当前期和前一期数据

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 包含当前期和前一期的技术指标数据
    """
    try:
        # 获取创业板ETF的日线数据，然后重采样为季线数据用于计算EMA指标
        # 需要足够的历史数据来计算EMA，季线需要更多日线数据
        # 因EMA的算法依赖上一期EMA，因此每次计算，都需要一次过获取所有日线数据
        required_daily_bars = ContextInfo.barpos + 1
        # 使用get_market_data_ex获取日线数据
        market_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close'],
            stock_code=[SIGNAL_FUND_CODE],
            period='1d',  # 改为日线
            end_time=g_current_bar_time.strftime('%Y%m%d'), #最新一期的数据，以当前执行逻辑的K线为准（如果是回测模式，就是每一根K线；如果是实盘模式，就是最新的K线）
            count=required_daily_bars,
            dividend_type='front',  # 前复权
            fill_data=True
        )

        #print("update_technical_indicators->market_data ==> ")
        #print(market_data)

        if market_data is None or len(market_data) == 0:
            log_message("WARNING", "技术指标更新", f"无法获取{SIGNAL_FUND_CODE}的日线数据", None, ContextInfo)
            return None

        # 获取具体股票的数据
        stock_data = market_data.get(SIGNAL_FUND_CODE)
        # print('[临时调试用]获取的股票数据列表 ===>')
        # print(stock_data)
        if stock_data is None or len(stock_data) == 0:
            log_message("WARNING", "技术指标更新", f"无法获取{SIGNAL_FUND_CODE}的日线数据", None, ContextInfo)
            return None

        # print('日线数据打印测试 ===>')
        # print(stock_data)

        try:
            quarterly_data = resample_daily_to_period(stock_data, EMA_DETECTION_CYCLE)
            if quarterly_data is None or len(quarterly_data) < 2:
                log_message("WARNING", "技术指标更新", f"无法重采样{SIGNAL_FUND_CODE}的季线数据或数据不足", None, ContextInfo)
                return None
        except Exception as e:
            log_message("ERROR", "技术指标更新", f"重采样失败: {str(e)}", None, ContextInfo)
            return None

        # print('季度线数据打印测试 ===>')
        # print(quarterly_data)

        try:
            print(f'quarterly_data type = {type(quarterly_data)}')
            if hasattr(quarterly_data['close'], 'values'):
                close_prices = quarterly_data['close'].values
                stock_dates = quarterly_data.index.tolist()
            else:
                close_prices = list(quarterly_data['close'])
                stock_dates = list(quarterly_data['index'])
        except Exception as e:
            print(f"获取收盘价数据失败: {str(e)}")
            return None

        # 计算EMA指标
        ema_values = calculate_ema(close_prices, EMA_PERIOD)
        # print('ema values ==> ')
        # print(ema_values)

        if len(ema_values) < 2:
            log_message("WARNING", "技术指标更新", "EMA计算结果不足，需要至少2个数据点", None, ContextInfo)
            return None

        # 获取当前期的数据
        current_ema = ema_values[-1]
        current_close = close_prices[-1]
        current_stock_date = stock_dates[-1]
        
        # 安全获取high数据 - 修复pandas兼容性问题
        try:
            if hasattr(quarterly_data['high'], 'values'):
                high_values = quarterly_data['high'].values
                current_high = high_values[-1]
                previous_high = high_values[-2]
            else:
                high_list = list(quarterly_data['high'])
                current_high = high_list[-1]
                previous_high = high_list[-2]
        except Exception as e:
            print(f"获取最高价数据失败: {str(e)}")
            # 使用收盘价作为备用
            current_high = close_prices[-1]
            previous_high = close_prices[-2]

        current_bottom_line = current_ema * BOTTOM_RATIO  # F1 = EMA * 0.85
        current_top_line = current_ema * TOP_RATIO        # F2 = EMA * 1.90

        # 获取前一期的数据
        previous_ema = ema_values[-2]
        previous_close = close_prices[-2]
        previous_stock_date = stock_dates[-2]
        previous_bottom_line = previous_ema * BOTTOM_RATIO
        previous_top_line = previous_ema * TOP_RATIO

        # 构建返回数据
        technical_data = {
            'current': {
                'ema_value': current_ema,
                'bottom_line': current_bottom_line,
                'top_line': current_top_line,
                'close': current_close,
                'high': current_high,
                'stock_date': current_stock_date
            },
            'previous': {
                'ema': previous_ema,
                'bottom_line': previous_bottom_line,
                'top_line': previous_top_line,
                'close': previous_close,
                'high': previous_high,
                'stock_date': previous_stock_date
            },
            'update_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # 记录技术指标更新日志
        log_message("INFO", "技术指标更新",
                   f"当前期 - EMA={current_ema:.4f}, 底部线={current_bottom_line:.4f}, 顶部线={current_top_line:.4f}, 收盘价={current_close:.4f}", None, ContextInfo)
        log_message("INFO", "技术指标更新",
                   f"前一期 - EMA={previous_ema:.4f}, 底部线={previous_bottom_line:.4f}, 顶部线={previous_top_line:.4f}, 收盘价={previous_close:.4f}", None, ContextInfo)

        return technical_data

    except Exception as e:
        error_msg = f"技术指标更新失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "技术指标更新", error_msg, None, ContextInfo)
        return None


def resample_daily_to_period(daily_data, period_type='1q', use_data_based_grouping=True):
    """
    将日线数据重采样为指定周期的数据，支持基于数据分布的智能分组

    Args:
        daily_data: 日线数据，包含 open, high, low, close 字段
        period_type: 周期类型，'1q'=季线, '1mon'=月线
        use_data_based_grouping: 是否使用基于数据分布的分组方法（推荐）

    Returns:
        重采样后的数据
    """
    try:
        import pandas as pd
    except ImportError:
        # 如果pandas不可用，返回None
        print("[重采样调试] pandas不可用，跳过重采样")
        return None

    # print(f"[重采样调试] 开始重采样，目标周期: {period_type}")

    try:
        # 如果数据为空，返回None
        if daily_data is None:
            print("[重采样调试] 输入数据为None")
            return None

        # print(f"[重采样调试] 输入数据类型: {type(daily_data)}")
        # print(f"[重采样调试] 输入数据形状: {daily_data.shape if hasattr(daily_data, 'shape') else '无shape属性'}")

        # 将数据转换为DataFrame（如果还不是的话）
        if not isinstance(daily_data, pd.DataFrame):
            # print(f"[重采样调试] 数据不是DataFrame，尝试转换...")
            if isinstance(daily_data, dict):
                daily_data = pd.DataFrame(daily_data)
                # print(f"[重采样调试] 从字典转换为DataFrame成功，形状: {daily_data.shape}")
            else:
                # print(f"[重采样调试] 无法转换数据类型，返回原数据")
                return daily_data

        # 确保数据有足够的行数
        if len(daily_data) == 0:
            # print("[重采样调试] 数据为空，返回原数据")
            return daily_data

        # print(f"[重采样调试] 数据列: {list(daily_data.columns)}")
        # print(f"[重采样调试] 当前索引类型: {type(daily_data.index)}")
        # print(f"[重采样调试] 索引前5个值: {daily_data.index[:5].tolist()}")

        # 如果没有时间索引，尝试将现有索引转换为时间索引
        if not isinstance(daily_data.index, pd.DatetimeIndex):
            # print("[重采样调试] 检测到非时间索引，尝试转换为时间索引...")

            try:
                # 尝试将索引转换为日期时间格式
                # 支持多种常见的日期格式
                original_index = daily_data.index.astype(str)
                # print(f"[重采样调试] 原始索引示例: {original_index[:3].tolist()}")

                # 尝试不同的日期格式
                date_formats = ['%Y%m%d', '%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y']
                new_index = None

                for fmt in date_formats:
                    try:
                        new_index = pd.to_datetime(original_index, format=fmt)
                        # print(f"[重采样调试] 成功使用格式 {fmt} 转换日期索引")
                        break
                    except:
                        continue

                # 如果所有格式都失败，尝试自动推断
                if new_index is None:
                    try:
                        new_index = pd.to_datetime(original_index)
                        # print("[重采样调试] 使用自动推断成功转换日期索引")
                    except:
                        # print("[重采样调试] 警告：无法解析日期格式，使用虚拟日期索引")
                        # 最后的备选方案：创建虚拟日期索引
                        end_date = pd.Timestamp.now().normalize()
                        start_date = end_date - pd.Timedelta(days=len(daily_data)-1)
                        new_index = pd.date_range(start=start_date, periods=len(daily_data), freq='D')

                daily_data.index = new_index
                # print(f"[重采样调试] 已创建时间索引: {daily_data.index[0]} 到 {daily_data.index[-1]}")

            except Exception as e:
                print(f"[重采样调试] 时间索引转换失败: {str(e)}")
                return daily_data

        # 根据周期类型设置重采样规则
        if period_type == '1q':
            rule = 'Q'  # 季度末
        elif period_type == '1mon':
            rule = 'M'  # 月末
        else:
            print(f"[重采样调试] 不支持的周期类型: {period_type}，返回原数据")
            return daily_data

        # print(f"[重采样调试] 使用重采样规则: {rule}")

        # 准备重采样的聚合规则
        agg_rules = {}
        for col in daily_data.columns:
            if col in ['open']:
                agg_rules[col] = 'first'
            elif col in ['high']:
                agg_rules[col] = 'max'
            elif col in ['low']:
                agg_rules[col] = 'min'
            elif col in ['close']:
                agg_rules[col] = 'last'
            elif col in ['volume', 'amount']:
                agg_rules[col] = 'sum'
            else:
                agg_rules[col] = 'last'  # 默认取最后一个值

        # print(f"[重采样调试] 聚合规则: {agg_rules}")

        # 选择重采样方法
        if use_data_based_grouping:
            # print("[重采样调试] 使用基于数据分布的智能分组方法...")
            resampled = resample_by_data_distribution(daily_data, period_type, agg_rules)
        else:
            # print("[重采样调试] 使用标准pandas重采样方法...")
            resampled = daily_data.resample(rule).agg(agg_rules).dropna()

        if resampled is None or len(resampled) == 0:
            # print("[重采样调试] 警告：重采样后数据为空！")
            return None

        # print(f"[重采样调试] 重采样完成！")
        # print(f"[重采样调试] 原始数据: {len(daily_data)} 行")
        # print(f"[重采样调试] 重采样后: {len(resampled)} 行")
        # print(f"[重采样调试] 重采样后索引: {resampled.index.tolist()}")

        return resampled

    except Exception as e:
        error_msg = f"重采样失败：{str(e)}"
        print(f"[重采样调试] 错误: {error_msg}")
        log_message("ERROR", "数据重采样", error_msg, None)
        # 重要：不要返回原数据，而是返回None或抛出异常
        raise Exception(f"数据重采样失败: {str(e)}")


def resample_by_data_distribution(daily_data, period_type, agg_rules):
    """
    基于数据分布的智能重采样方法
    通过分析日期间隔来确定周期边界，无需外部交易日历

    Args:
        daily_data: 日线数据
        period_type: 周期类型
        agg_rules: 聚合规则

    Returns:
        重采样后的数据
    """
    import pandas as pd

    try:
        # print("[智能分组] 开始分析数据分布...")

        # 获取所有日期
        dates = daily_data.index
        # print(f"[智能分组] 数据日期范围: {dates[0]} 到 {dates[-1]}")

        # 根据周期类型进行分组
        if period_type == '1q':
            groups = group_by_quarters(dates, daily_data)
        elif period_type == '1mon':
            groups = group_by_months(dates, daily_data)
        else:
            print(f"[智能分组] 不支持的周期类型: {period_type}")
            return None

        if not groups:
            print("[智能分组] 未能创建有效分组")
            return None

        # print(f"[智能分组] 创建了 {len(groups)} 个分组")

        # 对每个分组进行聚合
        result_data = []
        result_index = []

        for group_end_date, group_data in groups:
            # 应用聚合规则
            aggregated_row = {}
            for col, agg_func in agg_rules.items():
                if col in group_data.columns:
                    col_data = group_data[col]
                    if agg_func == 'first':
                        if hasattr(col_data, 'iloc'):
                            aggregated_row[col] = col_data.iloc[0]
                        else:
                            aggregated_row[col] = list(col_data)[0]
                    elif agg_func == 'last':
                        if hasattr(col_data, 'iloc'):
                            aggregated_row[col] = col_data.iloc[-1]
                        else:
                            aggregated_row[col] = list(col_data)[-1]
                    elif agg_func == 'max':
                        aggregated_row[col] = col_data.max()
                    elif agg_func == 'min':
                        aggregated_row[col] = col_data.min()
                    elif agg_func == 'sum':
                        aggregated_row[col] = col_data.sum()
                    else:
                        # 默认取最后一个
                        if hasattr(col_data, 'iloc'):
                            aggregated_row[col] = col_data.iloc[-1]
                        else:
                            aggregated_row[col] = list(col_data)[-1]

            result_data.append(aggregated_row)
            result_index.append(group_end_date)

        # 创建结果DataFrame
        result_df = pd.DataFrame(result_data, index=result_index)

        # print(f"[智能分组] 聚合完成，结果包含 {len(result_df)} 行")
        # print(f"[智能分组] 结果索引: {result_df.index.tolist()}")

        return result_df

    except Exception as e:
        print(f"[智能分组] 分组失败: {str(e)}")
        return None


def group_by_quarters(dates, daily_data):
    """
    按季度分组，基于数据分布确定季度边界
    """
    import pandas as pd

    groups = []
    current_quarter = None
    current_group_data = []
    current_group_start_idx = 0

    # print("[季度分组] 开始按季度分组...")

    for i, date in enumerate(dates):
        quarter = (date.month - 1) // 3 + 1
        year_quarter = (date.year, quarter)

        if current_quarter is None:
            current_quarter = year_quarter
            current_group_start_idx = i

        if year_quarter != current_quarter:
            # 季度变化，保存当前分组
            group_end_date = dates[i-1]  # 上一个日期作为季度末
            if hasattr(daily_data, 'iloc'):
                group_data = daily_data.iloc[current_group_start_idx:i]
            else:
                # 旧版pandas兼容
                group_data = daily_data[current_group_start_idx:i]
            groups.append((group_end_date, group_data))

            # print(f"[季度分组] 季度 {current_quarter[0]}Q{current_quarter[1]} 结束于 {group_end_date}，包含 {len(group_data)} 天数据")

            # 开始新的季度
            current_quarter = year_quarter
            current_group_start_idx = i

    # 处理最后一个季度
    if current_group_start_idx < len(dates) and current_quarter is not None:
        group_end_date = dates[-1]
        if hasattr(daily_data, 'iloc'):
            group_data = daily_data.iloc[current_group_start_idx:]
        else:
            # 旧版pandas兼容
            group_data = daily_data[current_group_start_idx:]
        groups.append((group_end_date, group_data))
        # print(f"[季度分组] 季度 {current_quarter[0]}Q{current_quarter[1]} 结束于 {group_end_date}，包含 {len(group_data)} 天数据")

    return groups


def group_by_months(dates, daily_data):
    """
    按月份分组，基于数据分布确定月份边界
    """
    import pandas as pd

    groups = []
    current_month = None
    current_group_data = []
    current_group_start_idx = 0

    # print("[月份分组] 开始按月份分组...")

    for i, date in enumerate(dates):
        year_month = (date.year, date.month)

        if current_month is None:
            current_month = year_month
            current_group_start_idx = i

        if year_month != current_month:
            # 月份变化，保存当前分组
            group_end_date = dates[i-1]  # 上一个日期作为月末
            if hasattr(daily_data, 'iloc'):
                group_data = daily_data.iloc[current_group_start_idx:i]
            else:
                # 旧版pandas兼容
                group_data = daily_data[current_group_start_idx:i]
            groups.append((group_end_date, group_data))

            # print(f"[月份分组] 月份 {current_month[0]}-{current_month[1]:02d} 结束于 {group_end_date}，包含 {len(group_data)} 天数据")

            # 开始新的月份
            current_month = year_month
            current_group_start_idx = i

    # 处理最后一个月份
    if current_group_start_idx < len(dates) and current_month is not None:
        group_end_date = dates[-1]
        if hasattr(daily_data, 'iloc'):
            group_data = daily_data.iloc[current_group_start_idx:]
        else:
            # 旧版pandas兼容
            group_data = daily_data[current_group_start_idx:]
        groups.append((group_end_date, group_data))
        # print(f"[月份分组] 月份 {current_month[0]}-{current_month[1]:02d} 结束于 {group_end_date}，包含 {len(group_data)} 天数据")

    return groups


def adjust_to_trading_dates(resampled_data, period_type, context_info):
    """
    将重采样后的数据索引调整为真实的交易日期末

    Args:
        resampled_data: 重采样后的数据
        period_type: 周期类型
        context_info: iQuant的ContextInfo对象

    Returns:
        调整后的数据或None
    """
    import pandas as pd

    try:
        print("[交易日调整] 开始调整重采样数据的日期索引...")

        # 获取数据的日期范围
        start_date = resampled_data.index[0].strftime('%Y%m%d')
        end_date = resampled_data.index[-1].strftime('%Y%m%d')

        print(f"[交易日调整] 数据日期范围: {start_date} 到 {end_date}")

        # 获取对应周期的交易日列表
        trading_dates = context_info.get_trading_dates('SH', start_date, end_date, -1, period_type)
        print(f'{start_date}到{end_date}的交易日列表 ===> ')
        print(trading_dates)

        if not trading_dates:
            print("[交易日调整] 未获取到交易日数据")
            return None

        print(f"[交易日调整] 获取到 {len(trading_dates)} 个交易日期")
        print(f"[交易日调整] 交易日期: {trading_dates}")

        # 将交易日期转换为DatetimeIndex
        trading_datetime_index = pd.to_datetime(trading_dates, format='%Y%m%d')

        # 检查数据长度是否匹配
        if len(trading_datetime_index) != len(resampled_data):
            print(f"[交易日调整] 警告：交易日数量({len(trading_datetime_index)})与重采样数据数量({len(resampled_data)})不匹配")
            # 如果交易日数量更多，取最后N个
            if len(trading_datetime_index) > len(resampled_data):
                trading_datetime_index = trading_datetime_index[-len(resampled_data):]
                print(f"[交易日调整] 调整为取最后 {len(resampled_data)} 个交易日")
            else:
                print("[交易日调整] 交易日数量不足，无法调整")
                return None

        # 创建新的DataFrame，使用交易日作为索引
        adjusted_data = resampled_data.copy()
        adjusted_data.index = trading_datetime_index

        print(f"[交易日调整] 调整完成，新的日期索引: {adjusted_data.index.tolist()}")

        return adjusted_data

    except Exception as e:
        print(f"[交易日调整] 调整失败: {str(e)}")
        return None


def simple_resample_to_quarterly(daily_data):
    """
    简化的季线重采样函数，避免pandas兼容性问题

    Args:
        daily_data: 日线数据

    Returns:
        dict: 季线数据
    """
    try:
        print("[简化重采样] 开始季线重采样...")

        if daily_data is None or len(daily_data) == 0:
            print("[简化重采样] 输入数据为空")
            return None

        # 获取数据
        try:
            if hasattr(daily_data, 'values'):
                # pandas DataFrame
                open_values = list(daily_data['open'].values)
                high_values = list(daily_data['high'].values)
                low_values = list(daily_data['low'].values)
                close_values = list(daily_data['close'].values)
            elif isinstance(daily_data, dict):
                # 字典格式
                open_values = list(daily_data['open'])
                high_values = list(daily_data['high'])
                low_values = list(daily_data['low'])
                close_values = list(daily_data['close'])
            else:
                print("[简化重采样] 不支持的数据格式")
                return None
        except Exception as e:
            print(f"[简化重采样] 数据提取失败: {str(e)}")
            return None

        data_length = len(close_values)
        print(f"[简化重采样] 输入数据长度: {data_length}")

        # 一季度约63个交易日，简化为60
        group_size = 60

        result_data = {
            'open': [],
            'high': [],
            'low': [],
            'close': []
        }

        # 分组处理
        for i in range(0, data_length, group_size):
            end_idx = min(i + group_size, data_length)

            if end_idx - i < group_size // 3:  # 如果剩余数据太少，合并到上一组
                if len(result_data['close']) > 0:
                    # 更新最后一组的数据
                    last_idx = len(result_data['close']) - 1
                    result_data['high'][last_idx] = max(result_data['high'][last_idx], max(high_values[i:end_idx]))
                    result_data['low'][last_idx] = min(result_data['low'][last_idx], min(low_values[i:end_idx]))
                    result_data['close'][last_idx] = close_values[end_idx - 1]
                break

            # 计算当前组的OHLC
            group_open = open_values[i]
            group_high = max(high_values[i:end_idx])
            group_low = min(low_values[i:end_idx])
            group_close = close_values[end_idx - 1]

            result_data['open'].append(group_open)
            result_data['high'].append(group_high)
            result_data['low'].append(group_low)
            result_data['close'].append(group_close)

        print(f"[简化重采样] 重采样完成，结果长度: {len(result_data['close'])}")
        return result_data

    except Exception as e:
        print(f"[简化重采样] 重采样失败: {str(e)}")
        return None


def calculate_ema(prices: List[float], period: int) -> List[float]:
    """
    计算指数移动平均线(EMA)
    采用类似东方财富终端的平滑曲线计算方法，从第1条K线开始模拟
    e1 = c1
    e2 = e1 + 2 / (p + 1) * (c - e1)
    
    Args:
        prices: 价格序列
        period: EMA周期

    Returns:
        List[float]: EMA值序列
    """
    if len(prices) < 1:
        return []

    ema_values = []
    multiplier = 2.0 / (period + 1)

    # 第一个EMA值使用简单移动平均
    sma = prices[0]
    ema_values.append(sma)

    # 计算后续的EMA值
    for i in range(1, len(prices)):
        # print(f'[调试输出马上删]，上一手ema = {ema_values[-1]}, multiplier = {multiplier}, period = {period}, close = {prices[i]}')
        ema = round(ema_values[-1] + multiplier * (prices[i] - ema_values[-1]), 3)
        ema_values.append(ema)

    return ema_values


def detect_signals(ContextInfo):
    """
    检测买入卖出信号
    基于EMA指标检测ENTERLONG和EXITLONG信号，并进行过滤验证

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 信号检测结果
    """
    try:
        # 获取技术指标数据
        technical_data = update_technical_indicators(ContextInfo)
        if technical_data is None:
            print("[信号检测] 技术指标数据为空，可能是数据不够，设置默认的非买非卖信号")
            return {
                'has_buy_signal': False,
                'has_sell_signal': False,
                'signal_details': None
            }

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 获取当前K线位置和日期
        current_kline_position = ContextInfo.barpos

        # 获取当前K线对应的日期（YYYYMMDD格式）
        try:
            current_bar_timestamp = ContextInfo.get_bar_timetag(current_kline_position)
            if current_bar_timestamp:
                kline_datetime = datetime.datetime.fromtimestamp(current_bar_timestamp / 1000)
                current_kline_date = kline_datetime.strftime("%Y%m%d")
            else:
                # 如果无法获取K线时间戳，使用当前日期
                log_message("WARNING", "信号检测", f'ContextInfo.get_bar_timetag无法获取K线(barpos = {current_kline_position})时间戳，使用当前日期', None, ContextInfo)
                current_kline_date = datetime.datetime.now().strftime("%Y%m%d")
        except Exception as e:
            log_message("WARNING", "信号检测", f"获取K线日期失败：{str(e)}", None, ContextInfo)
            current_kline_date = datetime.datetime.now().strftime("%Y%m%d")

        # 从新的数据结构中获取当前期和前一期数据
        current_data = technical_data['current']
        previous_data = technical_data['previous']

        ema_value = current_data['ema_value']
        bottom_line = current_data['bottom_line']
        top_line = current_data['top_line']
        current_close = current_data['close']
        current_high = current_data['high']
        current_stock_date = current_data['stock_date']

        # 检测买入信号：ENTERLONG = FILTER(CROSS(F1,C),8)
        # 即收盘价向下跌破底部线F1时触发
        has_buy_signal = False
        buy_signal_details = None
        # print(f'stock_date type = {type(current_stock_date)}, value = {current_stock_date}')
        current_month = current_stock_date.month
        current_day = current_stock_date.day
        # 买入信号，只会检测每个季度尾月
        if (current_month in [3, 12] and current_day >= 25) or (current_month in [6, 9] and current_day >= 24):
            print(f'当前K线日期在季度末7天内，检测买入信号 ===> {current_stock_date}')
            # test, will delete
            # prevclose = previous_data["close"]
            # prevbottom = previous_data["bottom_line"]
            # print(f'[买入信号打印调试]prevclose = {prevclose}, prevbottom = {prevbottom}, currclose = {current_close}, currbottom = {bottom_line}')
            # end test
            if (previous_data['close'] >= previous_data['bottom_line'] and
                current_close < bottom_line):
                # 发生了向下穿越(沉睡期 --> 激活期的信号)，检查信号过滤
                is_valid, filter_reason = check_signal_filter('ENTERLONG', current_time, current_kline_position)
                if is_valid:
                    has_buy_signal = True
                    buy_signal_details = {
                        'signal_type': 'ENTERLONG',
                        'signal_price': current_close,
                        'ema_value': ema_value,
                        'bottom_line': bottom_line,
                        'top_line': None,
                        'signal_time': current_time,
                        'kline_position': current_kline_position,
                        'kline_date': current_kline_date
                    }

                    # 记录信号到数据库
                    record_signal_to_db(buy_signal_details, True, None)
                    log_message("INFO", "信号检测", f"检测到买入信号：收盘价{current_close:.4f}跌破底部线{bottom_line:.4f}", None, ContextInfo)
                else:
                    # 记录被过滤的信号
                    filtered_signal = {
                        'signal_type': 'ENTERLONG',
                        'signal_price': current_close,
                        'ema_value': ema_value,
                        'bottom_line': bottom_line,
                        'top_line': None,
                        'signal_time': current_time,
                        'kline_position': current_kline_position,
                        'kline_date': current_kline_date
                    }
                    record_signal_to_db(filtered_signal, False, filter_reason)
                    log_message("INFO", "信号检测", f"买入信号被过滤：{filter_reason}", None, ContextInfo)

        # 检测卖出信号：EXITLONG = FILTER(CROSS(H,F2),10)
        # 即最高价向上突破顶部线F2时触发
        has_sell_signal = False
        sell_signal_details = None

        # test, will delete
        # prevhigh = previous_data['high']
        # prevtop = previous_data['top_line']
        # print(f'[卖出信号打印调试]prevhigh = {prevhigh}, prevtop = {prevtop}, currhigh = {current_high}, currtop = {top_line}')
        # end test

        if (previous_data['high'] <= previous_data['top_line'] and
            current_high > top_line):
            # 发生了向上穿越，检查信号过滤
            is_valid, filter_reason = check_signal_filter('EXITLONG', current_time, current_kline_position)
            if is_valid:
                has_sell_signal = True
                sell_signal_details = {
                    'signal_type': 'EXITLONG',
                    'signal_price': current_high,
                    'ema_value': ema_value,
                    'bottom_line': None,
                    'top_line': top_line,
                    'signal_time': current_time,
                    'kline_position': current_kline_position,
                    'kline_date': current_kline_date
                }

                # 记录信号到数据库
                record_signal_to_db(sell_signal_details, True, None)
                log_message("INFO", "信号检测", f"检测到卖出信号：最高价{current_high:.4f}突破顶部线{top_line:.4f}", None, ContextInfo)
            else:
                # 记录被过滤的信号
                filtered_signal = {
                    'signal_type': 'EXITLONG',
                    'signal_price': current_high,
                    'ema_value': ema_value,
                    'bottom_line': None,
                    'top_line': top_line,
                    'signal_time': current_time,
                    'kline_position': current_kline_position,
                    'kline_date': current_kline_date
                }
                record_signal_to_db(filtered_signal, False, filter_reason)
                log_message("INFO", "信号检测", f"卖出信号被过滤：{filter_reason}", None, ContextInfo)

        return {
            'has_buy_signal': has_buy_signal,
            'has_sell_signal': has_sell_signal,
            'buy_signal_details': buy_signal_details,
            'sell_signal_details': sell_signal_details
        }

    except Exception as e:
        error_msg = f"信号检测失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "信号检测", error_msg, None, ContextInfo)
        return {
            'has_buy_signal': False,
            'has_sell_signal': False,
            'signal_details': None
        }


def record_signal_to_db(signal_details: Dict, is_valid: bool, filter_reason: str = None):
    """
    记录信号到数据库

    Args:
        signal_details: 信号详情
        is_valid: 是否有效信号
        filter_reason: 过滤原因
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute("""
            INSERT INTO signal_history
            (signal_date, signal_type, signal_price, ema_value, bottom_line, top_line,
             kline_position, kline_date, is_valid, filter_reason, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            signal_details['signal_time'],
            signal_details['signal_type'],
            signal_details['signal_price'],
            signal_details['ema_value'],
            signal_details['bottom_line'],
            signal_details['top_line'],
            signal_details['kline_position'],
            signal_details['kline_date'],
            1 if is_valid else 0,
            filter_reason,
            current_time
        ))

        g_db_connection.commit()

    except Exception as e:
        log_message("ERROR", "信号记录", f"记录信号到数据库失败：{str(e)}", None)


def execute_trading_logic(ContextInfo, signal_result):
    """
    执行交易逻辑
    根据当前策略状态和信号结果执行相应的交易操作

    Args:
        ContextInfo: iQuant上下文信息对象
        signal_result: 信号检测结果
    """
    try:
        current_phase = g_strategy_status['current_phase']

        # 处理信号触发的状态切换
        if signal_result['has_buy_signal'] and current_phase == 'sleeping':
            # 买入信号：从沉睡期切换到激活期
            execute_phase_transition('sleeping', 'active', ContextInfo, signal_result['buy_signal_details'])

        elif signal_result['has_sell_signal'] and current_phase == 'active':
            # 卖出信号：从激活期切换到沉睡期
            execute_phase_transition('active', 'sleeping', ContextInfo, signal_result['sell_signal_details'])

        # 在激活期执行价值平均策略
        if g_strategy_status['current_phase'] == 'active':
            execute_value_averaging_strategy(ContextInfo)

        # 记录当前状态
        log_message("INFO", "交易逻辑",
                   f"交易逻辑执行完成，当前阶段：{g_strategy_status['current_phase']}", None, ContextInfo)

    except Exception as e:
        error_msg = f"执行交易逻辑失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "交易逻辑", error_msg, None, ContextInfo)


def execute_phase_transition_backtest(from_phase: str, to_phase: str, ContextInfo, signal_details: Dict):
    """
    回测模式的策略阶段切换

    特点：
    - 交易立即生效，无时差问题
    - 资金立即到账
    - 持仓立即更新
    - 逻辑相对简单

    Args:
        from_phase: 原阶段
        to_phase: 目标阶段
        ContextInfo: iQuant上下文信息对象
        signal_details: 信号详情
    """
    try:
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message("INFO", "阶段切换", f"[回测模式] 开始阶段切换：{from_phase} -> {to_phase}", None, ContextInfo)

        if from_phase == 'sleeping' and to_phase == 'active':
            # 沉睡期 -> 激活期：直接同步交易
            log_message("INFO", "阶段切换", "[回测模式] === 开始从沉睡期切换到激活期 ===", None, ContextInfo)

            # 1. 检查并卖出所有510720持仓
            position_510720 = get_current_position(SLEEPING_FUND_CODE)
            log_message("INFO", "阶段切换", f"[回测模式] 510720当前持仓：{position_510720['shares']}股，市值：{position_510720['market_value']:.2f}元", None, ContextInfo)

            if position_510720['shares'] > 0:
                log_message("INFO", "阶段切换", f"[回测模式] 准备卖出510720：{position_510720['shares']}股", None, ContextInfo)
                success = execute_trade_order_backtest(
                    SLEEPING_FUND_CODE, 'SELL', position_510720['shares'],
                    'SIGNAL_SELL', ContextInfo
                )
                if not success:
                    log_message("ERROR", "阶段切换", "[回测模式] 卖出510720失败，阶段切换中止", None, ContextInfo)
                    return
                else:
                    log_message("INFO", "阶段切换", f"[回测模式] 成功卖出510720：{position_510720['shares']}股", None, ContextInfo)
            else:
                log_message("INFO", "阶段切换", "[回测模式] 510720无持仓，跳过卖出步骤", None, ContextInfo)

            # 2. 设置价值平均起始期信息
            log_message("INFO", "阶段切换", "[回测模式] 设置价值平均起始期信息", None, ContextInfo)
            try:
                start_date, start_price = get_historical_highest_price(ACTIVE_FUND_CODE, 5, ContextInfo)
                g_strategy_status['start_period_date'] = start_date
                g_strategy_status['start_period_price'] = start_price
                log_message("INFO", "阶段切换", f"[回测模式] 价值平均起始期设置：日期={start_date}, 价格={start_price:.4f}", None, ContextInfo)
            except Exception as e:
                log_message("ERROR", "阶段切换", f"[回测模式] 设置价值平均起始期失败：{str(e)}", None, ContextInfo)
                # 使用默认值
                g_strategy_status['start_period_date'] = current_time[:10]  # YYYY-MM-DD
                g_strategy_status['start_period_price'] = get_current_price(ACTIVE_FUND_CODE, ContextInfo)
                log_message("WARNING", "阶段切换", f"[回测模式] 使用默认起始期：日期={g_strategy_status['start_period_date']}, 价格={g_strategy_status['start_period_price']:.4f}", None, ContextInfo)

            # 3. 按价值平均策略买入159915（回测模式直接买入，不触发异步逻辑）
            log_message("INFO", "阶段切换", "[回测模式] 按价值平均策略买入159915", None, ContextInfo)

            # 计算当前期数
            current_period = calculate_current_period(g_strategy_status['start_period_date'], ContextInfo)
            log_message("INFO", "阶段切换", f"[回测模式] 当前期数：{current_period}", None, ContextInfo)

            # 计算目标金额
            target_amount = current_period * PERIOD_INVESTMENT_AMOUNT
            log_message("INFO", "阶段切换", f"[回测模式] 目标金额：{target_amount:.2f}元（第{current_period}期 × {PERIOD_INVESTMENT_AMOUNT}元/期）", None, ContextInfo)

            # 获取当前价格
            current_price = get_current_price(ACTIVE_FUND_CODE, ContextInfo)
            log_message("INFO", "阶段切换", f"[回测模式] 159915当前价格：{current_price:.4f}元", None, ContextInfo)

            if current_price > 0:
                # 计算应买入股数
                shares_to_buy = int(target_amount / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
                log_message("INFO", "阶段切换", f"[回测模式] 按价值平均策略计算应买入股数：{shares_to_buy}股", None, ContextInfo)

                if shares_to_buy >= MIN_TRADE_SHARES:
                    log_message("INFO", "阶段切换", f"[回测模式] 准备买入159915：{shares_to_buy}股", None, ContextInfo)
                    # 回测模式直接买入，不会触发异步逻辑
                    success = execute_normal_buy_backtest(ACTIVE_FUND_CODE, shares_to_buy, ContextInfo)
                    if success:
                        log_message("INFO", "阶段切换", f"[回测模式] 成功买入159915：{shares_to_buy}股", None, ContextInfo)
                        # 更新策略状态
                        g_strategy_status['current_period'] = current_period
                        g_strategy_status['last_adjustment_period'] = current_period
                        log_message("INFO", "阶段切换", f"[回测模式] 已设置最后调整期数为：{current_period}", None, ContextInfo)

                        # 回测模式更新持仓记录
                        record_position_change_backtest(ACTIVE_FUND_CODE, 'BUY', shares_to_buy, ContextInfo)
                    else:
                        log_message("ERROR", "阶段切换", f"[回测模式] 买入159915失败：{shares_to_buy}股", None, ContextInfo)
                else:
                    log_message("WARNING", "阶段切换", f"[回测模式] 按价值平均策略计算的买入股数不足最小交易单位：{shares_to_buy} < {MIN_TRADE_SHARES}", None, ContextInfo)
            else:
                log_message("ERROR", "阶段切换", f"[回测模式] 无法获取159915当前价格，阶段切换失败", None, ContextInfo)

            # 4. 更新策略状态
            log_message("INFO", "阶段切换", "[回测模式] 更新策略状态", None, ContextInfo)
            g_strategy_status['current_phase'] = 'active'
            if g_strategy_status['first_activation_time'] is None:
                g_strategy_status['first_activation_time'] = current_time
                log_message("INFO", "阶段切换", f"[回测模式] 设置首次激活时间：{current_time}", None, ContextInfo)
            else:
                log_message("INFO", "阶段切换", f"[回测模式] 策略已激活过，首次激活时间：{g_strategy_status['first_activation_time']}", None, ContextInfo)

            # 保存策略状态到数据库
            update_strategy_status(current_time)

        elif from_phase == 'active' and to_phase == 'sleeping':
            # 激活期 -> 沉睡期：直接同步交易
            log_message("INFO", "阶段切换", "[回测模式] === 开始从激活期切换到沉睡期 ===", None, ContextInfo)

            # 1. 检查并卖出所有159915持仓
            position_159915 = get_current_position(ACTIVE_FUND_CODE)
            log_message("INFO", "阶段切换", f"[回测模式] 159915当前持仓：{position_159915['shares']}股，市值：{position_159915['market_value']:.2f}元", None, ContextInfo)

            if position_159915['shares'] > 0:
                log_message("INFO", "阶段切换", f"[回测模式] 准备卖出159915：{position_159915['shares']}股", None, ContextInfo)
                success = execute_trade_order_backtest(
                    ACTIVE_FUND_CODE, 'SELL', position_159915['shares'],
                    'SIGNAL_SELL', ContextInfo
                )
                if not success:
                    log_message("ERROR", "阶段切换", "[回测模式] 卖出159915失败，阶段切换中止", None, ContextInfo)
                    return
                else:
                    log_message("INFO", "阶段切换", f"[回测模式] 成功卖出159915：{position_159915['shares']}股", None, ContextInfo)
            else:
                log_message("INFO", "阶段切换", "[回测模式] 159915无持仓，跳过卖出步骤", None, ContextInfo)

            # 2. 使用所有资金买入510720
            log_message("INFO", "阶段切换", "[回测模式] 买入510720", None, ContextInfo)
            account_info = get_account_info(ContextInfo)
            available_cash = account_info['available_cash']
            current_price = get_current_price(SLEEPING_FUND_CODE, ContextInfo)

            log_message("INFO", "阶段切换", f"[回测模式] 账户可用资金：{available_cash:.2f}元", None, ContextInfo)
            log_message("INFO", "阶段切换", f"[回测模式] 510720当前价格：{current_price:.4f}元", None, ContextInfo)

            if available_cash > 0 and current_price > 0:
                shares_to_buy = int(available_cash / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
                log_message("INFO", "阶段切换", f"[回测模式] 计算可买入股数：{shares_to_buy}股", None, ContextInfo)

                if shares_to_buy >= MIN_TRADE_SHARES:
                    log_message("INFO", "阶段切换", f"[回测模式] 准备买入510720：{shares_to_buy}股", None, ContextInfo)
                    success = execute_normal_buy_backtest(SLEEPING_FUND_CODE, shares_to_buy, ContextInfo)
                    if success:
                        log_message("INFO", "阶段切换", f"[回测模式] 成功买入510720：{shares_to_buy}股", None, ContextInfo)
                    else:
                        log_message("ERROR", "阶段切换", f"[回测模式] 买入510720失败：{shares_to_buy}股", None, ContextInfo)
                else:
                    log_message("WARNING", "阶段切换", f"[回测模式] 可买入股数不足最小交易单位：{shares_to_buy} < {MIN_TRADE_SHARES}", None, ContextInfo)
            else:
                log_message("WARNING", "阶段切换", f"[回测模式] 无法买入510720：可用资金={available_cash:.2f}，当前价格={current_price:.4f}", None, ContextInfo)

            # 3. 更新策略状态
            log_message("INFO", "阶段切换", "[回测模式] 更新策略状态", None, ContextInfo)
            g_strategy_status['current_phase'] = 'sleeping'
            g_strategy_status['last_deactivation_time'] = current_time

            # 保存策略状态到数据库
            update_strategy_status(current_time)

        else:
            log_message("WARNING", "阶段切换", f"[回测模式] 不支持的阶段切换：{from_phase} -> {to_phase}", None, ContextInfo)

        log_message("INFO", "阶段切换", f"[回测模式] 阶段切换完成：{from_phase} -> {to_phase}", None, ContextInfo)

    except Exception as e:
        error_msg = f"[回测模式] 执行阶段切换失败：{str(e)}"
        log_message("ERROR", "阶段切换", error_msg, None, ContextInfo)


def execute_phase_transition_live(from_phase: str, to_phase: str, ContextInfo, signal_details: Dict):
    """
    实盘模式的策略阶段切换

    特点：
    - 使用异步任务系统
    - 处理时差和依赖关系
    - 避免重复交易
    - 逻辑相对复杂

    Args:
        from_phase: 原阶段
        to_phase: 目标阶段
        ContextInfo: iQuant上下文信息对象
        signal_details: 信号详情
    """
    try:
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message("INFO", "阶段切换", f"[实盘模式] 开始阶段切换：{from_phase} -> {to_phase}", None, ContextInfo)

        # 检查交易时点控制
        time_allowed, time_reason = is_trade_time_allowed(ContextInfo)
        log_message("DEBUG", "阶段切换", f"[实盘模式] 时点控制检查：{time_allowed}，原因：{time_reason}", None, ContextInfo)

        if not time_allowed:
            log_message("INFO", "阶段切换", f"[实盘模式] 未到交易时点，推迟阶段切换：{time_reason}", None, ContextInfo)
            return

        if from_phase == 'sleeping' and to_phase == 'active':
            # 沉睡期 -> 激活期：使用异步交易函数
            log_message("INFO", "阶段切换", "[实盘模式] === 开始从沉睡期切换到激活期 ===", None, ContextInfo)

            # 1. 设置价值平均起始期信息
            try:
                start_date, start_price = get_historical_highest_price(ACTIVE_FUND_CODE, 5, ContextInfo)
                g_strategy_status['start_period_date'] = start_date
                g_strategy_status['start_period_price'] = start_price
                log_message("INFO", "阶段切换", f"[实盘模式] 价值平均起始期设置：日期={start_date}, 价格={start_price:.4f}", None, ContextInfo)
            except Exception as e:
                log_message("ERROR", "阶段切换", f"[实盘模式] 设置价值平均起始期失败：{str(e)}", None, ContextInfo)
                g_strategy_status['start_period_date'] = current_time[:10]
                g_strategy_status['start_period_price'] = get_current_price(ACTIVE_FUND_CODE, ContextInfo)
                log_message("WARNING", "阶段切换", f"[实盘模式] 使用默认起始期：日期={g_strategy_status['start_period_date']}, 价格={g_strategy_status['start_period_price']:.4f}", None, ContextInfo)

            # 2. 计算初始买入股数
            current_period = calculate_current_period(g_strategy_status['start_period_date'], ContextInfo)
            target_amount = current_period * PERIOD_INVESTMENT_AMOUNT
            current_price = get_current_price(ACTIVE_FUND_CODE, ContextInfo)

            if current_price > 0:
                shares_to_buy = int(target_amount / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
                log_message("INFO", "阶段切换", f"[实盘模式] 计算初始买入：第{current_period}期，目标金额{target_amount:.2f}元，应买入{shares_to_buy}股", None, ContextInfo)

                if shares_to_buy >= MIN_TRADE_SHARES:
                    # 3. 使用现有的异步交易函数执行买入
                    task_group_id = execute_active_period_trade_async(
                        'BUY', shares_to_buy, 'PHASE_TRANSITION_BUY', ContextInfo
                    )

                    if task_group_id:
                        log_message("INFO", "阶段切换", f"[实盘模式] 已创建沉睡期到激活期转换任务组：{task_group_id}", task_group_id, ContextInfo)

                        # 4. 更新策略状态
                        g_strategy_status['current_phase'] = 'active'
                        g_strategy_status['current_period'] = current_period
                        g_strategy_status['last_adjustment_period'] = current_period
                        if g_strategy_status['first_activation_time'] is None:
                            g_strategy_status['first_activation_time'] = current_time
                            log_message("INFO", "阶段切换", f"[实盘模式] 设置首次激活时间：{current_time}", None, ContextInfo)

                        # 保存策略状态到数据库
                        update_strategy_status(current_time)

                        log_message("INFO", "阶段切换", f"[实盘模式] 沉睡期到激活期转换完成，任务组：{task_group_id}", None, ContextInfo)
                    else:
                        log_message("ERROR", "阶段切换", "[实盘模式] 创建沉睡期到激活期转换任务组失败", None, ContextInfo)
                        return
                else:
                    log_message("WARNING", "阶段切换", f"[实盘模式] 计算的买入股数不足最小交易单位：{shares_to_buy} < {MIN_TRADE_SHARES}", None, ContextInfo)
                    # 即使不买入，也要更新状态
                    g_strategy_status['current_phase'] = 'active'
                    g_strategy_status['current_period'] = current_period
                    update_strategy_status(current_time)
            else:
                log_message("ERROR", "阶段切换", "[实盘模式] 无法获取159915当前价格，阶段切换失败", None, ContextInfo)
                return

        elif from_phase == 'active' and to_phase == 'sleeping':
            # 激活期 -> 沉睡期：使用现有的异步转换函数
            log_message("INFO", "阶段切换", "[实盘模式] === 开始从激活期切换到沉睡期 ===", None, ContextInfo)

            # 使用现有的异步转换函数
            task_group_id = execute_active_to_sleeping_transition_async(ContextInfo, signal_details)
            if task_group_id:
                log_message("INFO", "阶段切换", f"[实盘模式] 已创建激活期到沉睡期转换任务组：{task_group_id}", task_group_id, ContextInfo)

                # 更新策略状态
                g_strategy_status['current_phase'] = 'sleeping'
                g_strategy_status['last_deactivation_time'] = current_time
                update_strategy_status(current_time)
            else:
                log_message("ERROR", "阶段切换", "[实盘模式] 创建阶段转换任务组失败", None, ContextInfo)
                return

        else:
            log_message("WARNING", "阶段切换", f"[实盘模式] 不支持的阶段切换：{from_phase} -> {to_phase}", None, ContextInfo)

        log_message("INFO", "阶段切换", f"[实盘模式] 阶段切换完成：{from_phase} -> {to_phase}", None, ContextInfo)

    except Exception as e:
        error_msg = f"[实盘模式] 执行阶段切换失败：{str(e)}"
        log_message("ERROR", "阶段切换", error_msg, None, ContextInfo)


def execute_phase_transition(from_phase: str, to_phase: str, ContextInfo, signal_details: Dict):
    """
    策略阶段切换主入口函数

    根据运行模式自动分发到对应的处理函数：
    - 回测模式：使用同步交易，逻辑简单
    - 实盘模式：使用异步任务系统，避免冲突

    Args:
        from_phase: 原阶段
        to_phase: 目标阶段
        ContextInfo: iQuant上下文信息对象
        signal_details: 信号详情
    """
    try:
        # 根据运行模式分发到对应的处理函数
        if is_backtest_mode(ContextInfo):
            log_message("INFO", "阶段切换", "检测到回测模式，使用回测专用阶段切换逻辑", None, ContextInfo)
            execute_phase_transition_backtest(from_phase, to_phase, ContextInfo, signal_details)
        else:
            log_message("INFO", "阶段切换", "检测到实盘模式，使用实盘专用阶段切换逻辑", None, ContextInfo)
            execute_phase_transition_live(from_phase, to_phase, ContextInfo, signal_details)

    except Exception as e:
        error_msg = f"执行阶段切换分发失败：{str(e)}"
        log_message("ERROR", "阶段切换", error_msg, None, ContextInfo)
        print(f"阶段切换异常详情：{traceback.format_exc()}")



def execute_value_averaging_strategy(ContextInfo):
    """
    执行价值平均策略
    在激活期根据价值平均公式调整159915持仓

    Args:
        ContextInfo: iQuant上下文信息对象
    """
    try:
        log_message("DEBUG", "价值平均", f"开始执行价值平均策略，投资周期：{INVESTMENT_CYCLE}", None, ContextInfo)

        # 检查是否为调整时机（根据投资周期）
        is_time = is_adjustment_time(ContextInfo)
        log_message("DEBUG", "价值平均", f"是否为调整时机：{is_time}", None, ContextInfo)

        if not is_time:
            log_message("INFO", "价值平均", "当前不是调整时机，跳过价值平均策略执行", None, ContextInfo)
            return

        # 检查交易时点控制（实盘模式）
        time_allowed, time_reason = is_trade_time_allowed(ContextInfo)
        log_message("DEBUG", "价值平均", f"时点控制检查：{time_allowed}，原因：{time_reason}", None, ContextInfo)

        if not time_allowed:
            log_message("INFO", "价值平均", f"未到交易时点，跳过价值平均策略执行：{time_reason}", None, ContextInfo)
            return

        # 获取当前159915价格
        current_price = get_current_price(ACTIVE_FUND_CODE, ContextInfo)
        if current_price <= 0:
            log_message("ERROR", "价值平均", "无法获取159915当前价格", None, ContextInfo)
            return

        # 计算价值平均策略
        va_result = calculate_value_averaging(current_price, ContextInfo)

        if va_result['trade_type'] in ['BUY', 'SELL'] and va_result['trade_shares'] > 0:
            # 使用统一的异步交易函数
            task_group_id = execute_active_period_trade_async(
                va_result['trade_type'], va_result['trade_shares'], 'VALUE_AVERAGE', ContextInfo
            )
            if task_group_id:
                action = "买入" if va_result['trade_type'] == 'BUY' else "卖出"
                log_message("INFO", "价值平均",
                           f"价值平均{action}任务已创建：{va_result['trade_shares']}股，"
                           f"期数：{va_result['current_period']}，任务组：{task_group_id}", None, ContextInfo)
                # 更新最后调整期数，防止重复调整
                update_last_adjustment_period(va_result['current_period'], ContextInfo)
            else:
                action = "买入" if va_result['trade_type'] == 'BUY' else "卖出"
                log_message("ERROR", "价值平均",
                           f"价值平均{action}任务创建失败：{va_result['trade_shares']}股", None, ContextInfo)
        else:
            log_message("INFO", "价值平均",
                       f"价值平均无需调整，期数：{va_result['current_period']}", None, ContextInfo)
            # 即使无需调整，也要更新最后调整期数，防止重复检查
            update_last_adjustment_period(va_result['current_period'], ContextInfo)

    except Exception as e:
        error_msg = f"执行价值平均策略失败：{str(e)}"
        log_message("ERROR", "价值平均", error_msg, None, ContextInfo)


def is_adjustment_time(ContextInfo=None) -> bool:
    """
    判断是否为价值平均调整时机
    确保每个周期只调整一次，防止重复买入

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 是否为调整时机
    """
    try:
        # 首先检查是否已经在当前期调整过
        if has_adjusted_in_current_period(ContextInfo):
            log_message("INFO", "调整时机", "当前期已经调整过，跳过本次调整", None, ContextInfo)
            return False

        # 支持多种投资周期格式
        if INVESTMENT_CYCLE in ["月线", "1mon"]:
            return is_period_adjustment_day("month", ContextInfo)
        elif INVESTMENT_CYCLE in ["日线", "1d"]:
            return True  # 每日调整
        # 不
        # elif INVESTMENT_CYCLE in ["季线", "1q"]:
        #     return is_period_adjustment_day("quarter", ContextInfo)
        # elif INVESTMENT_CYCLE in ["周线", "1w"]:
        #     return is_period_adjustment_day("week", ContextInfo)
        else:
            # 默认月线
            log_message("WARNING", "调整时机", f"未识别的投资周期格式：{INVESTMENT_CYCLE}，默认按月线处理", None, ContextInfo)
            return is_period_adjustment_day("month", ContextInfo)

    except Exception as e:
        log_message("ERROR", "调整时机", f"判断调整时机失败：{str(e)}", None, ContextInfo)
        return False


def has_adjusted_in_current_period(ContextInfo=None) -> bool:
    """
    检查当前期是否已经调整过
    防止同一个周期内重复调整

    双重防护机制：
    1. 基于期数的检查（原有机制）
    2. 基于当天交易记录的检查（新增机制）

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 当前期是否已经调整过
    """
    try:
        # === 第一重防护：基于期数的检查 ===
        period_adjusted = False
        if g_strategy_status and g_strategy_status.get('start_period_date'):
            # 计算当前期数
            current_period = calculate_current_period(g_strategy_status['start_period_date'], ContextInfo)

            # 获取最后调整的期数
            last_adjustment_period = g_strategy_status.get('last_adjustment_period', 0)

            # 如果当前期数等于最后调整期数，说明已经调整过
            period_adjusted = current_period == last_adjustment_period

            log_message("DEBUG", "期数检查",
                       f"当前期数：{current_period}，最后调整期数：{last_adjustment_period}，"
                       f"期数检查结果：{period_adjusted}", None, ContextInfo)

        # === 第二重防护：基于当天交易记录的检查 ===
        # 使用通用检查和专门的价值平均检查
        today_traded = check_today_trading_records(ContextInfo)
        va_traded_today = has_value_averaging_trade_today(ContextInfo)

        # 任一检查为True，都认为已经调整过
        has_adjusted = period_adjusted or today_traded or va_traded_today

        log_message("INFO", "重复检查",
                   f"期数检查：{period_adjusted}，通用交易检查：{today_traded}，"
                   f"价值平均检查：{va_traded_today}，最终结果：{has_adjusted}", None, ContextInfo)

        return has_adjusted

    except Exception as e:
        log_message("ERROR", "重复检查", f"检查是否已调整失败：{str(e)}", None, ContextInfo)
        return False


def check_today_trading_records(ContextInfo=None) -> bool:
    """
    检查当天是否已有交易记录
    防止同一天内重复交易

    检查范围：
    1. trade_orders 表中的当天交易记录
    2. trade_task_queue 表中的当天任务记录
    3. trade_execution_log 表中的当天执行记录

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 当天是否已有交易
    """
    try:
        if g_db_connection is None:
            log_message("WARNING", "当天交易检查", "数据库连接不可用，跳过检查", None, ContextInfo)
            return False

        cursor = g_db_connection.cursor()

        # 获取当天日期
        if ContextInfo and is_backtest_mode(ContextInfo):
            # 回测模式：使用当前K线时间
            current_time = get_current_time(ContextInfo)
            today = current_time.strftime("%Y-%m-%d")
            log_message("DEBUG", "当天交易检查", f"回测模式，使用K线日期：{today}", None, ContextInfo)
        else:
            # 实盘模式：使用系统时间
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            log_message("DEBUG", "当天交易检查", f"实盘模式，使用系统日期：{today}", None, ContextInfo)

        # === 检查1：trade_orders 表中的当天交易记录 ===
        cursor.execute("""
            SELECT COUNT(*) FROM trade_orders
            WHERE DATE(order_date) = ?
            AND order_status IN ('SUCCESS', 'PENDING')
            AND order_reason IN ('VALUE_AVERAGE', 'SIGNAL_BUY', 'SIGNAL_SELL')
        """, (today,))

        result = cursor.fetchone()
        today_orders = result[0] if result else 0

        if today_orders > 0:
            log_message("INFO", "当天交易检查",
                       f"发现当天已有{today_orders}笔交易订单，防止重复交易", None, ContextInfo)
            return True

        # === 检查2：trade_task_queue 表中的当天任务记录 ===
        cursor.execute("""
            SELECT COUNT(*) FROM trade_task_queue
            WHERE DATE(created_time) = ?
            AND task_status IN ('PENDING', 'EXECUTING', 'WAITING_CALLBACK', 'COMPLETED')
        """, (today,))

        result = cursor.fetchone()
        today_tasks = result[0] if result else 0

        if today_tasks > 0:
            log_message("INFO", "当天交易检查",
                       f"发现当天已有{today_tasks}个交易任务，防止重复交易", None, ContextInfo)
            return True

        # === 检查3：trade_execution_log 表中的当天执行记录 ===
        cursor.execute("""
            SELECT COUNT(*) FROM trade_execution_log
            WHERE DATE(trade_time) = ?
            AND status = 'SUCCESS'
        """, (today,))

        result = cursor.fetchone()
        today_executions = result[0] if result else 0

        if today_executions > 0:
            log_message("INFO", "当天交易检查",
                       f"发现当天已有{today_executions}笔成功执行的交易，防止重复交易", None, ContextInfo)
            return True

        # 所有检查都通过，当天没有交易记录
        log_message("DEBUG", "当天交易检查",
                   f"当天无交易记录：订单{today_orders}笔，任务{today_tasks}个，执行{today_executions}笔",
                   None, ContextInfo)
        return False

    except Exception as e:
        log_message("ERROR", "当天交易检查", f"检查当天交易记录失败：{str(e)}", None, ContextInfo)
        # 出错时为安全起见，返回True（防止重复交易）
        return True


def is_trade_time_allowed(ContextInfo=None) -> Tuple[bool, str]:
    """
    检查当前时点是否允许交易
    实盘模式：检查当前时间是否在设置的交易时点之后
    回测模式：忽略时点控制，直接返回允许

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        tuple: (是否允许交易, 说明信息)
    """
    try:
        # 回测模式忽略时点控制
        if ContextInfo and is_backtest_mode(ContextInfo):
            return (True, "回测模式，忽略时点控制")

        # 检查是否启用时点控制
        if not ENABLE_TIME_CONTROL:
            return (True, "时点控制已禁用")

        # 获取当前时间
        current_time = datetime.datetime.now()
        current_time_str = current_time.strftime("%H%M%S")

        # 解析设置的交易时点
        if len(TRADE_TIME_CONTROL) != 6:
            log_message("ERROR", "时点控制", f"交易时点格式错误：{TRADE_TIME_CONTROL}，应为HHmmss格式", None, ContextInfo)
            return (True, "时点格式错误，允许交易")

        trade_hour = int(TRADE_TIME_CONTROL[:2])
        trade_minute = int(TRADE_TIME_CONTROL[2:4])
        trade_second = int(TRADE_TIME_CONTROL[4:6])

        # 构造今天的交易时点
        trade_time = current_time.replace(hour=trade_hour, minute=trade_minute, second=trade_second, microsecond=0)

        # 检查当前时间是否在交易时点之后
        if current_time >= trade_time:
            return (True, f"当前时间{current_time.strftime('%H:%M:%S')}已过交易时点{TRADE_TIME_CONTROL[:2]}:{TRADE_TIME_CONTROL[2:4]}:{TRADE_TIME_CONTROL[4:6]}")
        else:
            return (False, f"当前时间{current_time.strftime('%H:%M:%S')}未到交易时点{TRADE_TIME_CONTROL[:2]}:{TRADE_TIME_CONTROL[2:4]}:{TRADE_TIME_CONTROL[4:6]}")

    except Exception as e:
        log_message("ERROR", "时点控制", f"检查交易时点失败：{str(e)}", None, ContextInfo)
        return (True, f"时点检查异常，允许交易：{str(e)}")


def has_value_averaging_trade_today(ContextInfo=None) -> bool:
    """
    专门检查当天是否已有价值平均交易
    更精确的检查，只针对价值平均策略的交易

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 当天是否已有价值平均交易
    """
    try:
        if g_db_connection is None:
            return False

        cursor = g_db_connection.cursor()

        # 获取当天日期
        if ContextInfo and is_backtest_mode(ContextInfo):
            current_time = get_current_time(ContextInfo)
            today = current_time.strftime("%Y-%m-%d")
        else:
            today = datetime.datetime.now().strftime("%Y-%m-%d")

        # 检查当天是否有价值平均相关的交易
        cursor.execute("""
            SELECT COUNT(*) FROM trade_orders
            WHERE DATE(order_date) = ?
            AND order_reason = 'VALUE_AVERAGE'
            AND order_status IN ('SUCCESS', 'PENDING')
        """, (today,))

        result = cursor.fetchone()
        va_trades = result[0] if result else 0

        if va_trades > 0:
            log_message("INFO", "价值平均检查",
                       f"当天已有{va_trades}笔价值平均交易，跳过本次调整", None, ContextInfo)
            return True

        # 检查当天是否有159915的交易任务（价值平均策略主要交易159915）
        cursor.execute("""
            SELECT COUNT(*) FROM trade_task_queue
            WHERE DATE(created_time) = ?
            AND stock_code = ?
            AND task_status IN ('PENDING', 'EXECUTING', 'WAITING_CALLBACK', 'COMPLETED')
        """, (today, ACTIVE_FUND_CODE))

        result = cursor.fetchone()
        va_tasks = result[0] if result else 0

        if va_tasks > 0:
            log_message("INFO", "价值平均检查",
                       f"当天已有{va_tasks}个{ACTIVE_FUND_CODE}交易任务，跳过本次调整", None, ContextInfo)
            return True

        log_message("DEBUG", "价值平均检查",
                   f"当天无价值平均交易：订单{va_trades}笔，任务{va_tasks}个", None, ContextInfo)
        return False

    except Exception as e:
        log_message("ERROR", "价值平均检查", f"检查当天价值平均交易失败：{str(e)}", None, ContextInfo)
        return True  # 出错时为安全起见返回True


def is_period_adjustment_day(period_type: str, ContextInfo=None) -> bool:
    """
    判断是否为周期调整日
    每个周期只在特定的一天进行调整
    回测模式下使用当前K线时间，实盘模式下使用系统时间

    TODO 此处后期需要再升级，没有识别假日之类的能力

    Args:
        period_type: 周期类型 ("month", "quarter", "week")
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 是否为调整日
    """
    try:
        # 回测模式下使用当前K线时间，实盘模式下使用系统时间
        if ContextInfo and is_backtest_mode(ContextInfo):
            current_date = get_current_time(ContextInfo)
        else:
            current_date = datetime.datetime.now()

        log_message("DEBUG", "调整日判断",
                   f"使用时间：{current_date.strftime('%Y-%m-%d')}，"
                   f"周期类型：{period_type}，"
                   f"回测模式：{is_backtest_mode(ContextInfo) if ContextInfo else False}")

        if period_type == "month":
            # 月线：每月最后一个工作日
            return is_last_trading_day_of_month(current_date)
        elif period_type == "quarter":
            # 季线：每季度最后一个工作日
            return (current_date.month in [3, 6, 9, 12] and
                   is_last_trading_day_of_month(current_date))
        elif period_type == "week":
            # 周线：每周五
            return current_date.weekday() == 4  # 4 = Friday
        else:
            return False

    except Exception as e:
        log_message("ERROR", "调整日判断", f"判断调整日失败：{str(e)}", None, ContextInfo)
        return False


def is_last_trading_day_of_month(current_date: datetime.datetime) -> bool:
    """
    判断是否为月末最后一个交易日
    更精确的判断，确保每月只调整一次

    Args:
        current_date: 当前日期

    Returns:
        bool: 是否为月末最后交易日
    """
    try:
        # 如果是周末，不是交易日
        if current_date.weekday() >= 5:  # 5=Saturday, 6=Sunday
            return False

        # 获取当月最后一天
        if current_date.month == 12:
            next_month = current_date.replace(year=current_date.year + 1, month=1, day=1)
        else:
            next_month = current_date.replace(month=current_date.month + 1, day=1)

        last_day_of_month = next_month - datetime.timedelta(days=1)

        # 从月末最后一天往前找，找到第一个工作日
        check_date = last_day_of_month
        while check_date.weekday() >= 5:  # 跳过周末
            check_date -= datetime.timedelta(days=1)

        # 当前日期是否为月末最后一个工作日
        is_last_trading_day = current_date.date() == check_date.date()

        log_message("DEBUG", "月末判断",
                   f"当前日期：{current_date.strftime('%Y-%m-%d')}，"
                   f"月末最后交易日：{check_date.strftime('%Y-%m-%d')}，"
                   f"是否匹配：{is_last_trading_day}")

        return is_last_trading_day

    except Exception as e:
        log_message("ERROR", "月末判断", f"判断月末最后交易日失败：{str(e)}", None)
        return False


def update_last_adjustment_period(current_period: int, ContextInfo=None):
    """
    更新最后调整期数，防止重复调整

    Args:
        current_period: 当前期数
        ContextInfo: iQuant上下文信息对象
    """
    try:
        if g_strategy_status is None:
            log_message("WARNING", "调整记录", "策略状态为空，无法更新调整记录", None, ContextInfo)
            return

        # 更新内存中的策略状态
        g_strategy_status['last_adjustment_period'] = current_period
        g_strategy_status['last_adjustment_time'] = get_current_time_str(ContextInfo)

        # 更新数据库中的策略状态
        if g_db_connection is not None:
            cursor = g_db_connection.cursor()
            # TODO 这里确认一下，last_adjustment_time和updated_time应该是一样的，结合下方的代码来说，但实际上数据库里不是
            cursor.execute("""
                UPDATE strategy_status
                SET last_adjustment_period = ?,
                    last_adjustment_time = ?,
                    updated_time = ?
                WHERE id = (SELECT MAX(id) FROM strategy_status)
            """, (
                current_period,
                g_strategy_status['last_adjustment_time'],
                g_strategy_status['last_adjustment_time']
            ))
            g_db_connection.commit()

            log_message("INFO", "调整记录",
                       f"已更新最后调整期数：{current_period}，时间：{g_strategy_status['last_adjustment_time']}", None, ContextInfo)
        else:
            log_message("WARNING", "调整记录", "数据库连接为空，无法保存调整记录", None, ContextInfo)

    except Exception as e:
        log_message("ERROR", "调整记录", f"更新最后调整期数失败：{str(e)}", None, ContextInfo)


def calculate_value_averaging(current_price: float, ContextInfo=None) -> Dict:
    """
    计算价值平均策略
    基于5年历史最高点计算当前期数和目标金额

    实现需求中的详细案例逻辑：
    - 以历史最高点所在K线作为第1期
    - 每期目标金额 = 期数 × 每期投入金额
    - 根据当前价格重新计算持仓价值
    - 计算需要买入或卖出的份额

    Args:
        current_price: 当前价格

    Returns:
        dict: 价值平均计算结果
    """
    try:
        # 获取或计算价值平均起始期信息
        if g_strategy_status['start_period_date'] is None:
            # 首次计算，需要找到5年内的历史最高点
            start_date, start_price = get_historical_highest_price(ACTIVE_FUND_CODE, 5, ContextInfo)

            # 更新策略状态
            g_strategy_status['start_period_date'] = start_date
            g_strategy_status['start_period_price'] = start_price

            log_message("INFO", "价值平均计算",
                       f"设定价值平均起始期：日期={start_date}, 价格={start_price:.4f}", None, ContextInfo)

        start_date = g_strategy_status['start_period_date']
        start_price = g_strategy_status['start_period_price']

        # 计算当前期数
        current_period = calculate_current_period(start_date, ContextInfo)

        if current_period < 0:  # 修复：期数0是有效的（表示刚开始激活期）
            log_message("WARNING", "价值平均计算", f"当前期数计算异常：{current_period}", None, ContextInfo)
            return {
                'current_period': 0,
                'target_amount': 0,
                'current_value': 0,
                'trade_amount': 0,
                'trade_shares': 0,
                'trade_type': 'HOLD'
            }

        # 计算目标金额：期数 × 每期投入金额
        # 例如：第3期目标金额 = 3 × 10000 = 30000元
        target_amount = current_period * PERIOD_INVESTMENT_AMOUNT

        # 获取当前159915持仓信息
        current_position = get_current_position(ACTIVE_FUND_CODE)
        current_shares = current_position.get('shares', 0)

        # 重要调试信息：记录当前持仓状态
        log_message("DEBUG", "价值平均计算",
                   f"开始计算第{current_period}期：当前持仓={current_shares}股，"
                   f"持仓记录={current_position}", None, ContextInfo)

        # 按当前价格重新计算持仓价值
        # 例如：持有666股，当前价格80元，持仓价值 = 666 × 80 = 53280元
        current_value = current_shares * current_price

        # 计算需要调整的金额
        # 例如：目标30000元 - 当前价值53280元 = -23280元（需要卖出）
        trade_amount = target_amount - current_value

        # 记录详细的价值平均计算过程（按需求案例格式）
        log_message("INFO", "价值平均详细计算",
                   f"第{current_period}期计算：目标金额={target_amount}元，"
                   f"当前持仓={current_shares}股，当前价格={current_price:.2f}元，"
                   f"持仓价值={current_value:.2f}元，需要调整={trade_amount:.2f}元", None, ContextInfo)

        # 确定交易类型和股数
        if abs(trade_amount) < MIN_TRADE_SHARES * current_price:
            # 金额太小，不执行交易
            trade_type = 'HOLD'
            trade_shares = 0
            trade_amount = 0
        elif trade_amount > 0:
            # 需要买入
            trade_type = 'BUY'
            # 计算需要买入的股数（必须是100的倍数）
            # 例如：需要买入4219元，价格30元，4219/30=140.63股，取整为140股
            trade_shares = int(trade_amount / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
            trade_amount = trade_shares * current_price

            log_message("INFO", "价值平均买入",
                       f"需要买入{trade_amount:.2f}元，即{trade_shares}股", None, ContextInfo)
        else:
            # 需要卖出
            trade_type = 'SELL'
            # 计算需要卖出的股数（必须是100的倍数）
            # 例如：需要卖出23280元，价格80元，23280/80=291股，取整为200股
            sell_amount = abs(trade_amount)
            ideal_shares = int(sell_amount / current_price)
            # 卖出必须是MIN_TRADE_SHARES的整数倍，且最少100股
            trade_shares = max(MIN_TRADE_SHARES, int(ideal_shares / MIN_TRADE_SHARES) * MIN_TRADE_SHARES)

            # 重要：确保卖出股数不超过当前持仓
            if trade_shares > current_shares:
                log_message("WARNING", "价值平均卖出",
                           f"计算卖出股数{trade_shares}超过当前持仓{current_shares}，调整为持仓数量",
                           None, ContextInfo)
                trade_shares = current_shares

            trade_amount = -trade_shares * current_price  # 负数表示卖出

            log_message("INFO", "价值平均卖出",
                       f"需要卖出{sell_amount:.2f}元，即{trade_shares}股", None, ContextInfo)

        # 更新当前期数到策略状态
        g_strategy_status['current_period'] = current_period

        result = {
            'current_period': current_period,
            'target_amount': target_amount,
            'current_value': current_value,
            'current_shares': current_shares,
            'trade_amount': trade_amount,
            'trade_shares': trade_shares,
            'trade_type': trade_type,
            'calculation_details': {
                'start_date': start_date,
                'start_price': start_price,
                'current_price': current_price,
                'period_investment': PERIOD_INVESTMENT_AMOUNT
            }
        }

        log_message("INFO", "价值平均计算",
                   f"价值平均计算完成：期数={current_period}，目标={target_amount}，"
                   f"当前价值={current_value:.2f}，交易类型={trade_type}，交易股数={trade_shares}", None, ContextInfo)

        return result

    except Exception as e:
        error_msg = f"价值平均计算失败：{str(e)}"
        log_message("ERROR", "价值平均计算", error_msg, None, ContextInfo)
        return {
            'current_period': 0,
            'target_amount': 0,
            'current_value': 0,
            'trade_amount': 0,
            'trade_shares': 0,
            'trade_type': 'HOLD'
        }


def calculate_current_period(start_date: str, ContextInfo=None) -> int:
    """
    计算当前期数
    基于起始日期和投资周期计算当前是第几期

    Args:
        start_date: 起始日期字符串
        ContextInfo: iQuant上下文信息对象（回测模式下使用）

    Returns:
        int: 当前期数
    """
    try:
        # 解析起始日期
        start_dt = datetime.datetime.strptime(start_date, "%Y-%m-%d")

        # 获取当前时间（回测适配）
        if ContextInfo and is_backtest_mode(ContextInfo):
            current_dt = get_current_time(ContextInfo)
        else:
            current_dt = datetime.datetime.now()

        # 根据投资周期计算期数
        if INVESTMENT_CYCLE in ["月线", "1mon"]:
            # 按月计算期数
            months_diff = (current_dt.year - start_dt.year) * 12 + (current_dt.month - start_dt.month)
            return max(1, months_diff + 1)  # 至少是第1期
        elif INVESTMENT_CYCLE in ["季线", "1q"]:
            # 按季度计算期数
            quarters_diff = ((current_dt.year - start_dt.year) * 4 +
                           (current_dt.month - 1) // 3 - (start_dt.month - 1) // 3)
            return max(1, quarters_diff + 1)
        elif INVESTMENT_CYCLE in ["日线", "1d"]:
            # 按交易日计算期数（简化为自然日）
            days_diff = (current_dt - start_dt).days
            return max(1, days_diff + 1)
        elif INVESTMENT_CYCLE in ["周线", "1w"]:
            # 按周计算期数
            weeks_diff = (current_dt - start_dt).days // 7
            return max(1, weeks_diff + 1)
        else:
            # 默认按月计算
            months_diff = (current_dt.year - start_dt.year) * 12 + (current_dt.month - start_dt.month)
            return max(1, months_diff + 1)

    except Exception as e:
        log_message("ERROR", "期数计算", f"计算当前期数失败：{str(e)}", None, ContextInfo)
        return 1  # 出错时返回第1期


def get_current_position(stock_code: str) -> Dict:
    """
    获取指定股票的当前持仓信息
    使用交易记录重新计算持仓，确保准确性

    Args:
        stock_code: 股票代码

    Returns:
        dict: 持仓信息
    """
    try:
        # 方法1：从交易记录重新计算持仓（更可靠）
        calculated_position = calculate_position_from_trades(stock_code)
        if calculated_position['shares'] >= 0:
            return calculated_position

        # 方法2：从数据库查询最新持仓记录（备用）
        # cursor = g_db_connection.cursor()
        # cursor.execute("""
        #     SELECT shares, avg_cost, market_value, current_price
        #     FROM position_records
        #     WHERE stock_code = ?
        #     ORDER BY record_date DESC
        #     LIMIT 1
        # """, (stock_code,))

        # result = cursor.fetchone()
        # if result:
        #     return {
        #         'shares': result[0],
        #         'avg_cost': result[1],
        #         'market_value': result[2],
        #         'current_price': result[3]
        #     }
        # else:
        #     # 没有持仓记录，返回空持仓
        #     return {
        #         'shares': 0,
        #         'avg_cost': 0,
        #         'market_value': 0,
        #         'current_price': 0
        #     }

    except Exception as e:
        log_message("ERROR", "持仓查询", f"查询持仓信息失败：{str(e)}", None)
        return {
            'shares': 0,
            'avg_cost': 0,
            'market_value': 0,
            'current_price': 0
        }


def calculate_position_from_trades(stock_code: str) -> Dict:
    """
    从数据库中的交易记录重新计算当前持仓
    这是最可靠的持仓计算方法，避免数据库持仓记录错误

    Args:
        stock_code: 股票代码

    Returns:
        dict: 计算出的持仓信息
    """
    try:
        if g_db_connection is None:
            return {'shares': -1, 'avg_cost': 0, 'market_value': 0, 'current_price': 0}

        cursor = g_db_connection.cursor()

        # 查询所有交易记录，按时间顺序
        cursor.execute("""
            SELECT order_date, order_type, actual_shares, actual_price
            FROM trade_orders
            WHERE stock_code = ? AND actual_shares > 0
            ORDER BY order_date ASC
        """, (stock_code,))

        trades = cursor.fetchall()

        total_shares = 0
        total_cost = 0.0
        last_price = 0.0

        for trade in trades:
            order_date, order_type, shares, price = trade
            last_price = price

            if order_type == 'BUY':
                # 买入：增加持仓和成本
                total_cost += shares * price
                total_shares += shares
            elif order_type == 'SELL':
                # 卖出：减少持仓，按比例减少成本
                if total_shares > 0:
                    cost_per_share = total_cost / total_shares
                    total_cost -= shares * cost_per_share
                    total_shares -= shares
                    total_shares = max(0, total_shares)  # 确保不为负数
                    total_cost = max(0, total_cost)     # 确保不为负数

        # 计算平均成本
        avg_cost = total_cost / total_shares if total_shares > 0 else 0

        # 计算市值（使用最后交易价格）
        market_value = total_shares * last_price if last_price > 0 else 0

        return {
            'shares': total_shares,
            'avg_cost': avg_cost,
            'market_value': market_value,
            'current_price': last_price
        }

    except Exception as e:
        # 计算失败，返回错误标识
        return {'shares': -1, 'avg_cost': 0, 'market_value': 0, 'current_price': 0}


def execute_trade_order_backtest(stock_code: str, order_type: str, shares: int, order_reason: str, ContextInfo) -> bool:
    """
    执行交易指令 -- 回测版
    回测模式下使用passorder进行真实模拟交易，实盘模式执行真实交易

    Args:
        stock_code: 股票代码
        order_type: 订单类型 ('BUY' 或 'SELL')
        shares: 交易股数
        order_reason: 下单原因
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 交易是否成功
    """
    try:
        if shares <= 0:
            log_message("WARNING", "交易执行", f"交易股数无效：{shares}", None, ContextInfo)
            return False

        # 记录交易指令到数据库
        order_id = record_trade_order(stock_code, order_type, shares, order_reason, ContextInfo)

        # 获取当前价格
        current_price = get_current_price(stock_code, ContextInfo)
        trade_amount = shares * current_price

        # 检查是否为回测模式
        if is_backtest_mode(ContextInfo):
            # 回测模式：使用passorder进行真实模拟交易
            log_message("INFO", "交易执行",
                       f"[回测模式] 执行{order_type} {stock_code} {shares}股 "
                       f"价格={current_price:.4f} 金额={trade_amount:.2f} 原因={order_reason}", None, ContextInfo)

            success = execute_backtest_trade(stock_code, order_type, shares, ContextInfo)

            if success:
                # 更新交易指令状态
                update_trade_order_status(order_id, 'SUCCESS', shares, current_price)
                log_message("INFO", "交易执行", f"[回测模式] 交易成功：{order_type} {stock_code} {shares}股", None, ContextInfo)
            else:
                update_trade_order_status(order_id, 'FAILED', 0, current_price)
                log_message("ERROR", "交易执行", f"[回测模式] 交易失败：{order_type} {stock_code} {shares}股", None, ContextInfo)

            return success
        

    except Exception as e:
        error_msg = f"执行交易指令失败：{str(e)}"
        print(error_msg)
        log_message("ERROR", "交易执行", error_msg, None, ContextInfo)
        return False


def execute_backtest_trade(stock_code: str, order_type: str, shares: int, ContextInfo) -> bool:
    """
    执行回测模式下的真实模拟交易
    使用passorder函数，让iQuant平台能够跟踪策略收益

    Args:
        stock_code: 股票代码
        order_type: 订单类型 ('BUY' 或 'SELL')
        shares: 交易股数
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 交易是否成功
    """
    try:
        log_message("INFO", "回测交易", f"开始执行回测交易：{order_type} {stock_code} {shares}股", None, ContextInfo)

        # 设置ContextInfo的stock属性（passorder需要）
        ContextInfo.stock = stock_code

        # 确保ContextInfo有accountid属性
        if not hasattr(ContextInfo, 'accountid'):
            ContextInfo.accountid = ACCOUNT_ID

        if order_type == 'BUY':
            # 回测模式买入
            # passorder(23, 1101, C.accountid, C.stock, 5, -1, vol, C)
            log_message("INFO", "回测交易", f"[回测买入] 调用passorder买入{shares}股{stock_code}", None, ContextInfo)

            result = passorder(
                33 if ACCOUNT_TYPE == "CREDIT" else 23,  # 买入
                1101,  # 单股、单账号、普通、股/手方式下单
                ACCOUNT_ID,  # 账户ID
                ContextInfo.stock,  # 股票代码
                5,  # prType=5 (市价)
                -1,  # price=-1 (市价)
                shares,  # 股数
                ContextInfo  # 上下文
            )

            log_message("INFO", "回测交易", f"[回测买入] passorder结果：{result}", None, ContextInfo)

        elif order_type == 'SELL':
            # 回测模式卖出
            # passorder(24, 1101, C.accountid, C.stock, 5, -1, holding_vol, C)
            log_message("INFO", "回测交易", f"[回测卖出] 调用passorder卖出{shares}股{stock_code}", None, ContextInfo)

            result = passorder(
                34 if ACCOUNT_TYPE == "CREDIT" else 24,  # 卖出
                1101,  # 单股、单账号、普通、股/手方式下单
                ACCOUNT_ID,  # 账户ID
                ContextInfo.stock,  # 股票代码
                5,  # prType=5 (市价)
                -1,  # price=-1 (市价)
                shares,  # 股数
                ContextInfo  # 上下文
            )

            log_message("INFO", "回测交易", f"[回测卖出] passorder结果：{result}", None, ContextInfo)

        else:
            log_message("ERROR", "回测交易", f"未知的订单类型：{order_type}", None, ContextInfo)
            return False

        # 在回测模式下，假设所有passorder调用都成功（100%成功率）
        log_message("INFO", "回测交易", f"[回测模式] passorder执行完成，假设100%成功", None, ContextInfo)

        # 记录持仓变化到数据库（让后续能查询到持仓）
        record_position_change_backtest(stock_code, order_type, shares, ContextInfo)

        return True

    except Exception as e:
        log_message("ERROR", "回测交易", f"回测交易执行失败：{str(e)}", None, ContextInfo)
        print(f"回测交易错误详情: {traceback.format_exc()}")
        return False


def record_position_change_backtest(stock_code: str, order_type: str, shares: int, ContextInfo):
    """
    记录持仓变化到数据库（回测模式专用）
    确保后续查询能获取到正确的持仓信息

    Args:
        stock_code: 股票代码
        order_type: 订单类型 ('BUY' 或 'SELL')
        shares: 股数
        ContextInfo: iQuant上下文信息对象
    """
    try:
        if g_db_connection is None:
            log_message("WARNING", "持仓记录", "数据库连接为空，无法记录持仓变化", None, ContextInfo)
            return

        current_time = get_current_time_str(ContextInfo)
        current_price = get_current_price(stock_code, ContextInfo)

        # 获取当前持仓
        current_position = get_current_position(stock_code)
        current_shares = current_position.get('shares', 0)
        current_avg_cost = current_position.get('avg_cost', 0)

        if order_type == 'BUY':
            # 买入：增加持仓
            new_shares = current_shares + shares
            if current_shares > 0:
                # 计算新的平均成本
                total_cost = current_shares * current_avg_cost + shares * current_price
                new_avg_cost = total_cost / new_shares
            else:
                new_avg_cost = current_price

        elif order_type == 'SELL':
            # 卖出：减少持仓
            new_shares = max(0, current_shares - shares)
            new_avg_cost = current_avg_cost  # 卖出不改变平均成本

        else:
            log_message("ERROR", "持仓记录", f"未知的订单类型：{order_type}", None, ContextInfo)
            return

        # 计算新的市值
        new_market_value = new_shares * current_price

        # 获取当前期数（仅对ACTIVE_FUND_CODE有效）
        period_number = None
        target_value = None
        if stock_code == ACTIVE_FUND_CODE and g_strategy_status:
            period_number = g_strategy_status.get('current_period', 0)
            if period_number > 0:
                target_value = period_number * PERIOD_INVESTMENT_AMOUNT

        # 插入新的持仓记录
        cursor = g_db_connection.cursor()
        cursor.execute("""
            INSERT INTO position_records
            (record_date, stock_code, shares, avg_cost, market_value, current_price,
             period_number, target_value, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            current_time, stock_code, new_shares, new_avg_cost, new_market_value,
            current_price, period_number, target_value, current_time
        ))

        g_db_connection.commit()

        log_message("INFO", "持仓记录",
                   f"[回测模式] 持仓更新：{stock_code} {new_shares}股 "
                   f"平均成本={new_avg_cost:.4f} 市值={new_market_value:.2f}", None, ContextInfo)

    except Exception as e:
        log_message("ERROR", "持仓记录", f"记录持仓变化失败：{str(e)}", None, ContextInfo)



def execute_normal_buy_backtest(stock_code: str, shares: int, ContextInfo) -> bool:
    """
    执行普通买入操作（仅下单，不做其他判断）

    Args:
        stock_code: 股票代码
        shares: 买入股数
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 是否成功
    """
    try:
        # 使用passorder函数执行普通买入
        passorder(
            opType = 33 if ACCOUNT_TYPE == "CREDIT" else 23,           # 股票买入
            orderType=1101,      # 单股、单账号、普通、股/手方式下单
            accountid=ACCOUNT_ID,  # 账户ID
            orderCode=stock_code,         # 股票代码
            prType=44,            # 对手方最优价格
            price=-1,            # 价格（最新价时无效）
            volume=shares,       # 股数
            strategyName="GYTrading2",  # 策略名
            quickTrade=1,        # 立即下单
            userOrderId="",      # 用户自定义订单ID，可不填写
            ContextInfo=ContextInfo
        )

        return True

    except Exception as e:
        log_message("ERROR", "普通买入", f"执行普通买入失败：{str(e)}", None, ContextInfo)
        return False


def execute_sell_order_backtest(stock_code: str, shares: int, ContextInfo) -> bool:
    """
    执行卖出订单

    Args:
        stock_code: 股票代码
        shares: 卖出股数
        ContextInfo: iQuant上下文信息对象

    Returns:
        bool: 是否成功
    """
    try:
        # 使用passorder函数执行卖出
        # opType=24: 股票卖出

        passorder(
            opType = 34 if ACCOUNT_TYPE == "CREDIT" else 24,           # 股票卖出
            orderType=1101,      # 单股、单账号、普通、股/手方式下单
            accountid=ACCOUNT_ID,  # 账户ID
            orderCode=stock_code,         # 股票代码
            prType=5,            # 最新价
            price=-1,            # 价格（最新价时无效）
            volume=shares,       # 股数
            strategyName="GYTrading2",  # 策略名
            quickTrade=1,        # 立即下单
            userOrderId="",      # 用户订单ID
            ContextInfo=ContextInfo
        )

        return True

    except Exception as e:
        log_message("ERROR", "卖出执行", f"执行卖出失败：{str(e)}", None, ContextInfo)
        return False


def record_trade_order(stock_code: str, order_type: str, shares: int, order_reason: str,
                      order_uuid: str = None, ContextInfo=None) -> int:
    """
    记录交易指令到数据库（回测适配）

    Args:
        stock_code: 股票代码
        order_type: 订单类型
        shares: 交易股数
        order_reason: 下单原因
        order_uuid: 订单UUID（用于与异步任务关联）
        ContextInfo: iQuant上下文信息对象

    Returns:
        int: 订单ID
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = get_current_time_str(ContextInfo) if ContextInfo else datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, 'PENDING', ?, ?)
        """, (current_time, stock_code, order_type, order_reason, shares, order_uuid, current_time))

        order_id = cursor.lastrowid
        g_db_connection.commit()

        return order_id

    except Exception as e:
        log_message("ERROR", "订单记录", f"记录交易指令失败：{str(e)}", None, ContextInfo)
        return -1


def update_trade_order_status(order_id: int, status: str, actual_shares: int = 0,
                            actual_price: float = 0, error_message: str = None):
    """
    更新交易指令状态

    Args:
        order_id: 订单ID
        status: 订单状态
        actual_shares: 实际成交股数
        actual_price: 实际成交价格
        error_message: 错误信息
    """
    try:
        if order_id <= 0:
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        cursor.execute("""
            UPDATE trade_orders SET
            order_status = ?, actual_shares = ?, actual_price = ?,
            error_message = ?, execution_time = ?
            WHERE id = ?
        """, (status, actual_shares, actual_price, error_message, current_time, order_id))

        g_db_connection.commit()

    except Exception as e:
        log_message("ERROR", "订单更新", f"更新交易指令状态失败：{str(e)}", None)


def get_current_price(stock_code: str, ContextInfo) -> float:
    """
    获取股票当前价格（回测适配）

    Args:
        stock_code: 股票代码
        ContextInfo: iQuant上下文信息对象

    Returns:
        float: 当前价格
    """
    try:
        # 检查ContextInfo是否有效
        if ContextInfo is None:
            log_message("WARNING", "价格获取", f"ContextInfo为None，无法获取{stock_code}实时价格，返回默认价格", None, None)
            # 返回一个合理的默认价格
            if stock_code == ACTIVE_FUND_CODE:  # 159915.SZ
                return 0.0  # 默认价格
            elif stock_code == SLEEPING_FUND_CODE:  # 510720.SH
                return 0.0  # 默认价格
            else:
                return 0.0  # 其他股票默认价格

        if is_backtest_mode(ContextInfo):
            # 回测模式：获取当前K线的收盘价
            return get_backtest_current_price(stock_code, ContextInfo)
        else:
            # 实盘模式：获取最新价格
            return get_realtime_current_price(stock_code, ContextInfo)

    except Exception as e:
        log_message("ERROR", "价格获取", f"获取{stock_code}价格失败：{str(e)}", None, ContextInfo)
        return 0.0


def get_backtest_current_price(stock_code: str, ContextInfo) -> float:
    """
    获取回测模式下的当前价格（当前K线收盘价）

    Args:
        stock_code: 股票代码
        ContextInfo: iQuant上下文信息对象

    Returns:
        float: 当前K线收盘价
    """
    try:
        # 获取当前K线的数据
        market_data = ContextInfo.get_market_data_ex(
            fields=['close'],
            stock_code=[stock_code],
            period='1d',  # 日线数据
            count=1,  # 只获取当前K线
            end_time=g_current_bar_time.strftime('%Y%m%d'),  # 当前K线时间
            dividend_type='front',
            fill_data=True
        )
        
        if market_data is not None and len(market_data) > 0:
            stock_data = market_data.get(stock_code)
            if stock_data is not None and len(stock_data) > 0:
                close_data = stock_data['close']
                if hasattr(close_data, 'iloc'):
                    price = float(close_data.iloc[-1])
                else:
                    # 旧版pandas兼容
                    close_list = list(close_data)
                    price = float(close_list[-1])

                log_message("INFO", "价格获取", f"[回测模式] {stock_code}当前K线收盘价：{price:.4f}", None, ContextInfo)
                return price

        log_message("WARNING", "价格获取", f"[回测模式] 无法获取{stock_code}的当前K线价格数据", None, ContextInfo)
        return 0.0

    except Exception as e:
        log_message("ERROR", "价格获取", f"[回测模式] 获取{stock_code}当前K线价格失败：{str(e)}", None, ContextInfo)
        print(traceback.format_exc())
        return 0.0


def get_realtime_current_price(stock_code: str, ContextInfo) -> float:
    """
    获取实盘模式下的当前价格（最新价格）

    Args:
        stock_code: 股票代码
        ContextInfo: iQuant上下文信息对象

    Returns:
        float: 最新价格
    """
    try:
        # 使用get_market_data_ex获取最新价格
        market_data = ContextInfo.get_market_data_ex(
            fields=['close'],
            stock_code=[stock_code],
            period='1min',  # 1分钟线获取最新价格
            count=1,
            dividend_type='front',
            fill_data=True
        )

        if market_data is not None and len(market_data) > 0:
            stock_data = market_data.get(stock_code)
            if stock_data is not None and len(stock_data) > 0:
                close_data = stock_data['close']
                if hasattr(close_data, 'iloc'):
                    price = float(close_data.iloc[-1])
                else:
                    # 旧版pandas兼容
                    close_list = list(close_data)
                    price = float(close_list[-1])

                log_message("INFO", "价格获取", f"[实盘模式] {stock_code}最新价格：{price:.4f}", None, ContextInfo)
                return price

        log_message("WARNING", "价格获取", f"[实盘模式] 无法获取{stock_code}的价格数据", None, ContextInfo)
        return 0.0

    except Exception as e:
        log_message("ERROR", "价格获取", f"[实盘模式] 获取{stock_code}价格失败：{str(e)}", None, ContextInfo)
        return 0.0


def get_account_info(ContextInfo) -> Dict:
    """
    获取账户信息
    查询账户资金、持仓、融资额度等信息

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 账户信息
    """
    try:
        # 使用get_trade_detail_data获取账户资金信息
        account_data = get_trade_detail_data(
            ACCOUNT_ID,
            ACCOUNT_TYPE,
            'ACCOUNT'
        )
        
        if account_data and len(account_data) > 0:
            account = account_data[0]
            # print(f'account_data ===> ')
            # print(vars(account))
            # 获取基本资金信息
            total_assets = getattr(account, 'm_dBalance', 0)  # 总资产
            available_cash = getattr(account, 'm_dAvailable', 0)  # 可用资金
            # 获取融资信息（如果是信用账户）
            credit_limit = getattr(account, 'm_dFinMaxQuota', 0)  # 融资额度
            credit_available = getattr(account, 'm_dFinEnableQuota', 0)  # 可用融资

            # 获取持仓信息  TODO 看似已无用，待处理
            positions = get_all_positions(ContextInfo)

            account_info = {
                'total_assets': total_assets,
                'available_cash': available_cash,
                'credit_limit': credit_limit,
                'credit_available': credit_available,
                'positions': positions
            }

            # 记录账户信息到数据库
            record_account_info(account_info)

            return account_info
        else:
            log_message("WARNING", "账户信息", "无法获取账户数据", None, ContextInfo)
            return {
                'total_assets': 0,
                'available_cash': 0,
                'credit_limit': 0,
                'credit_available': 0,
                'positions': {}
            }

    except Exception as e:
        log_message("ERROR", "账户信息", f"获取账户信息失败：{str(e)}", None, ContextInfo)
        return {
            'total_assets': 0,
            'available_cash': 0,
            'credit_limit': 0,
            'credit_available': 0,
            'positions': {}
        }


def get_all_positions(ContextInfo) -> Dict:
    """
    TODO 这个方法已无意义，通过错误的get_trade_detail_data参数，必定获取失败，待处理
    获取所有持仓信息

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 持仓信息字典
    """
    try:
        positions = {}

        # 获取持仓数据
        position_data = get_trade_detail_data(
            ACCOUNT_ID,
            'ACCOUNT',
            'POSITION'
        )

        if position_data:
            for pos in position_data:
                stock_code = getattr(pos, 'm_strInstrumentID', '')
                shares = getattr(pos, 'm_nVolume', 0)
                avg_cost = getattr(pos, 'm_dOpenCost', 0)
                current_price = getattr(pos, 'm_dSettlementPrice', 0)
                market_value = shares * current_price

                if stock_code and shares > 0:
                    positions[stock_code] = {
                        'shares': shares,
                        'avg_cost': avg_cost,
                        'current_price': current_price,
                        'market_value': market_value
                    }

                    # 记录持仓到数据库
                    record_position(stock_code, shares, avg_cost, market_value, current_price)

        return positions

    except Exception as e:
        log_message("ERROR", "持仓查询", f"获取持仓信息失败：{str(e)}", None, ContextInfo)
        return {}


def record_account_info(account_info: Dict):
    """
    记录账户信息到数据库
    使用 UPSERT 逻辑避免重复记录

    Args:
        account_info: 账户信息
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 使用 INSERT OR REPLACE 逻辑，避免重复插入
        cursor.execute("""
            INSERT OR REPLACE INTO account_info
            (account_id, total_assets, available_cash, credit_limit, credit_available,
             update_time, created_time)
            VALUES (?, ?, ?, ?, ?, ?,
                    COALESCE((SELECT created_time FROM account_info WHERE account_id = ?), ?))
        """, (
            ACCOUNT_ID,  # 使用实际的账户ID
            account_info['total_assets'],
            account_info['available_cash'],
            account_info['credit_limit'],
            account_info['credit_available'],
            current_time,
            ACCOUNT_ID,  # 用于查询现有记录的created_time
            current_time  # 如果是新记录则使用当前时间
        ))

        g_db_connection.commit()

    except Exception as e:
        log_message("ERROR", "账户记录", f"记录账户信息失败：{str(e)}", None)


def record_position(stock_code: str, shares: int, avg_cost: float,
                   market_value: float, current_price: float):
    """
    记录持仓信息到数据库

    Args:
        stock_code: 股票代码
        shares: 持仓股数
        avg_cost: 平均成本
        market_value: 市值
        current_price: 当前价格
    """
    try:
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 如果是159915，还需要记录期数和目标价值
        period_number = None
        target_value = None
        if stock_code == ACTIVE_FUND_CODE and g_strategy_status:
            period_number = g_strategy_status.get('current_period', 0)
            if period_number > 0:
                target_value = period_number * PERIOD_INVESTMENT_AMOUNT

        cursor.execute("""
            INSERT INTO position_records
            (record_date, stock_code, shares, avg_cost, market_value, current_price,
             period_number, target_value, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            current_time, stock_code, shares, avg_cost, market_value, current_price,
            period_number, target_value, current_time
        ))

        g_db_connection.commit()

    except Exception as e:
        log_message("ERROR", "持仓记录", f"记录持仓信息失败：{str(e)}", None)


def get_historical_highest_price(stock_code: str, years: int = 5, ContextInfo=None) -> Tuple[str, float]:
    """
    获取指定年限内的历史最高价
    用于价值平均策略的起始期计算

    Args:
        stock_code: 股票代码
        years: 回溯年数
        ContextInfo: iQuant上下文信息对象

    Returns:
        tuple: (最高价日期, 最高价)
    """
    try:
        # 计算起始日期（years年前）- 回测适配
        if ContextInfo:
            end_date = get_current_time(ContextInfo)
        else:
            end_date = datetime.datetime.now()
        start_date = end_date - datetime.timedelta(days=years * 365)

        start_date_str = start_date.strftime("%Y%m%d")
        end_date_str = end_date.strftime("%Y%m%d")

        # 使用get_market_data_ex获取历史数据
        # 注意：这里需要在实际运行环境中调用，回测时可能需要不同的处理
        try:
            # 获取日线数据，然后重采样为月线数据来查找最高价
            market_data = ContextInfo.get_market_data_ex(
                fields=['high', 'close'],
                stock_code=[stock_code],
                period='1d',  # 改为日线
                start_time=start_date_str,
                end_time=end_date_str,
                dividend_type='front',
                fill_data=True
            )

            if market_data is None or len(market_data) == 0:
                log_message("WARNING", "历史最高价查询", f"无法获取{stock_code}的历史数据", None, ContextInfo)
                # 返回默认值
                return (start_date.strftime("%Y-%m-%d"), 10.0)

            # 获取具体股票的数据
            stock_data = market_data.get(stock_code)
            if stock_data is None or len(stock_data) == 0:
                log_message("WARNING", "历史最高价查询", f"无法获取{stock_code}的历史数据", None, ContextInfo)
                # 返回默认值
                return (start_date.strftime("%Y-%m-%d"), 0.0)

            # 将日线数据重采样为月线数据
            try:
                monthly_data = resample_daily_to_period(stock_data, '1mon')
                if monthly_data is None or len(monthly_data) == 0:
                    log_message("WARNING", "历史最高价查询", f"无法重采样{stock_code}的月线数据", None, ContextInfo)
                    # 返回默认值
                    return (start_date.strftime("%Y-%m-%d"), 0.0)

                log_message("INFO", "历史最高价查询", f"成功获取{stock_code}的{len(monthly_data)}个月的数据", None, ContextInfo)

            except Exception as e:
                log_message("ERROR", "历史最高价查询", f"重采样失败: {str(e)}", None, ContextInfo)
                # 返回默认值
                return (start_date.strftime("%Y-%m-%d"), 10.0)

            # 找到最高价(收盘价)及其对应的日期
            try:
                close_data = monthly_data['close']

                # 检查是否有有效数据
                if close_data.empty or close_data.isna().all():
                    log_message("WARNING", "历史最高价查询", f"{stock_code}的收盘价数据为空或全为NaN", None, ContextInfo)
                    return (start_date.strftime("%Y-%m-%d"), 10.0)

                # 使用idxmax()找到最高价对应的日期索引
                max_high_idx = close_data.idxmax()  # 返回的是索引值（日期），不是位置
                log_message("DEBUG", "历史最高价查询", f"最高价索引：{max_high_idx} (类型：{type(max_high_idx)})", None, ContextInfo)

                # 直接使用索引值获取价格和日期
                max_high_price = close_data.loc[max_high_idx]  # 使用.loc根据索引值获取数据
                max_high_date = max_high_idx  # idxmax()返回的就是索引值（日期）

                log_message("DEBUG", "历史最高价查询", f"找到最高价：{max_high_price}，日期：{max_high_date}", None, ContextInfo)

            except Exception as e:
                log_message("ERROR", "历史最高价查询", f"查找最高价失败: {str(e)}", None, ContextInfo)
                # 返回默认值
                return (start_date.strftime("%Y-%m-%d"), 10.0)

            # 格式化日期
            if hasattr(max_high_date, 'strftime'):
                date_str = max_high_date.strftime("%Y-%m-%d")
            else:
                # 如果是字符串格式的日期，尝试转换
                try:
                    date_obj = datetime.datetime.strptime(str(max_high_date)[:8], "%Y%m%d")
                    date_str = date_obj.strftime("%Y-%m-%d")
                except:
                    date_str = start_date.strftime("%Y-%m-%d")

            log_message("INFO", "历史最高价查询",
                       f"{stock_code}在{years}年内最高价：{max_high_price:.4f}，日期：{date_str}", None, ContextInfo)

            return (date_str, float(max_high_price))

        except Exception as api_error:
            log_message("WARNING", "历史最高价查询",
                       f"API调用失败：{str(api_error, None, ContextInfo)}，使用默认值")
            # API调用失败时返回默认值
            return (start_date.strftime("%Y-%m-%d"), 10.0)

    except Exception as e:
        error_msg = f"获取历史最高价失败：{str(e)}"
        log_message("ERROR", "历史最高价查询", error_msg, None, ContextInfo)
        # 返回默认值
        default_date = (datetime.datetime.now() - datetime.timedelta(days=years * 365)).strftime("%Y-%m-%d")
        return (default_date, 10.0)


def check_signal_filter(signal_type: str, signal_date: str, current_kline_position: int) -> Tuple[bool, str]:
    """
    检查信号过滤条件
    实现FILTER功能，防止信号重复触发
    基于K线距离进行过滤：买入信号8个周期内不重复，卖出信号10个周期内不重复

    Args:
        signal_type: 信号类型 ('ENTERLONG' 或 'EXITLONG')
        signal_date: 信号日期
        current_kline_position: 当前K线位置

    Returns:
        tuple: (是否有效, 过滤原因)
    """
    try:
        # 确定过滤周期数
        if signal_type == 'ENTERLONG':
            filter_periods = BUY_SIGNAL_FILTER_PERIODS  # 8个周期
        elif signal_type == 'EXITLONG':
            filter_periods = SELL_SIGNAL_FILTER_PERIODS  # 10个周期
        else:
            return (False, f"未知信号类型：{signal_type}")

        # 查询历史信号记录
        cursor = g_db_connection.cursor()

        # 获取最近一次相同类型的有效信号记录
        cursor.execute("""
            SELECT signal_date, signal_type, kline_position FROM signal_history
            WHERE signal_type = ? AND is_valid = 1
            ORDER BY signal_date DESC
            LIMIT 1
        """, (signal_type,))

        recent_signal = cursor.fetchone()

        if recent_signal is None:
            # 没有历史信号，当前信号有效
            return (True, None)

        # 获取上次信号的K线位置
        last_kline_position = recent_signal[2]

        if last_kline_position is None:
            # 如果历史记录中没有K线位置信息，允许信号通过
            log_message("WARNING", "信号过滤", f"历史信号缺少K线位置信息，允许信号通过", None)
            return (True, None)

        # 计算K线距离
        kline_distance = current_kline_position - last_kline_position

        if kline_distance <= filter_periods:
            return (False, f"距离上次{signal_type}信号仅{kline_distance}根K线，小于等于过滤周期{filter_periods}根K线")
        else:
            return (True, None)

    except Exception as e:
        error_msg = f"信号过滤检查失败：{str(e)}"
        log_message("ERROR", "信号过滤", error_msg, None)
        return (True, None)  # 出错时允许信号通过，避免错过重要信号


# ==================== 策略状态管理辅助函数 ====================

def get_strategy_phase_duration() -> int:
    """
    获取当前策略阶段持续时间（天数）

    Returns:
        int: 持续天数
    """
    try:
        if g_strategy_status is None:
            return 0

        last_check_time = g_strategy_status.get('last_check_time')
        if not last_check_time:
            return 0

        last_time = datetime.datetime.strptime(last_check_time, "%Y-%m-%d %H:%M:%S")
        current_time = datetime.datetime.now()

        return (current_time - last_time).days

    except Exception as e:
        log_message("ERROR", "状态管理", f"获取阶段持续时间失败：{str(e)}", None)
        return 0


def validate_strategy_state() -> bool:
    """
    验证策略状态的一致性

    Returns:
        bool: 状态是否一致
    """
    try:
        if g_strategy_status is None:
            log_message("WARNING", "状态验证", "策略状态为空", None)
            return False

        current_phase = g_strategy_status.get('current_phase')
        if current_phase not in ['sleeping', 'active']:
            log_message("ERROR", "状态验证", f"无效的策略阶段：{current_phase}", None)
            return False

        # 验证激活期状态
        if current_phase == 'active':
            start_period_date = g_strategy_status.get('start_period_date')
            if not start_period_date:
                log_message("WARNING", "状态验证", "激活期缺少起始期日期", None)
                return False

            current_period = g_strategy_status.get('current_period', 0)
            if current_period < 0:  # 修复：期数0是有效的（表示刚开始激活期）
                log_message("WARNING", "状态验证", f"激活期期数异常：{current_period}", None)
                return False

        return True

    except Exception as e:
        log_message("ERROR", "状态验证", f"验证策略状态失败：{str(e)}", None)
        return False


def reset_strategy_state():
    """
    重置策略状态到初始状态
    """
    try:
        global g_strategy_status

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        g_strategy_status = {
            'id': g_strategy_status.get('id') if g_strategy_status else None,
            'current_phase': 'sleeping',
            'last_check_time': current_time,
            'first_activation_time': None,
            'start_period_date': None,
            'start_period_price': None,
            'current_period': 0,
            'created_time': g_strategy_status.get('created_time', current_time) if g_strategy_status else current_time,
            'updated_time': current_time
        }

        # 更新到数据库
        update_strategy_status(current_time)

        log_message("INFO", "状态管理", "策略状态已重置", None)

    except Exception as e:
        log_message("ERROR", "状态管理", f"重置策略状态失败：{str(e)}", None)


def force_activate_strategy(ContextInfo=None):
    """
    强制激活策略（客户部署专用）
    自动计算或使用手动设置的买入信号，直接进入激活期

    Args:
        ContextInfo: iQuant上下文信息对象
    """
    try:
        global g_strategy_status

        if not FORCE_ACTIVE_MODE:
            log_message("INFO", "强制激活", "强制激活模式未开启，跳过", None, ContextInfo)
            return False

        current_time = get_current_time_str(ContextInfo)

        # 1. 检查是否已经在激活期
        if g_strategy_status and g_strategy_status['current_phase'] == 'active':
            log_message("INFO", "强制激活", "策略已处于激活期，无需重复激活", None, ContextInfo)
            return True

        # 2. 获取激活点信息（自动计算或手动设置）
        if AUTO_CALCULATE_ACTIVATION:
            log_message("INFO", "强制激活", "开始自动计算最近的激活期买点...", None, ContextInfo)
            activation_info = calculate_latest_activation_point(ContextInfo)
            if activation_info is None:
                log_message("ERROR", "强制激活", "自动计算激活点失败，回退到手动设置", None, ContextInfo)
                activation_info = {
                    'date': FORCE_ACTIVE_START_DATE,
                    'price': FORCE_ACTIVE_START_PRICE,
                    'source': 'manual_fallback'
                }
        else:
            log_message("INFO", "强制激活", "使用手动设置的激活点", None, ContextInfo)
            activation_info = {
                'date': FORCE_ACTIVE_START_DATE,
                'price': FORCE_ACTIVE_START_PRICE,
                'source': 'manual'
            }

        # 3. 模拟插入买入信号记录
        success = insert_simulated_buy_signal_with_info(activation_info, ContextInfo)
        if not success:
            log_message("ERROR", "强制激活", "模拟买入信号插入失败", None, ContextInfo)
            return False

        # 4. 设置策略状态为激活期
        g_strategy_status['current_phase'] = 'active'
        g_strategy_status['first_activation_time'] = activation_info['date'] + " 15:00:00"
        g_strategy_status['start_period_date'] = activation_info['date']
        g_strategy_status['start_period_price'] = activation_info['price']
        g_strategy_status['current_period'] = 0  # 将在价值平均策略中重新计算
        g_strategy_status['last_check_time'] = current_time
        g_strategy_status['updated_time'] = current_time

        # 5. 更新到数据库
        update_strategy_status(current_time)

        log_message("INFO", "强制激活",
                   f"策略已强制激活 - 起始日期：{activation_info['date']}, "
                   f"起始价格：{activation_info['price']:.4f}, "
                   f"数据来源：{activation_info['source']}", None, ContextInfo)

        return True

    except Exception as e:
        error_msg = f"强制激活策略失败：{str(e)}"
        log_message("ERROR", "强制激活", error_msg, None, ContextInfo)
        return False


def calculate_latest_activation_point(ContextInfo=None):
    """
    自动计算最近的激活期买点
    分析历史数据，找到最近一次满足买入信号条件的点

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 包含date, price, source等信息，失败时返回None
    """
    try:
        if ContextInfo is None:
            log_message("ERROR", "自动计算", "ContextInfo为空，无法获取历史数据", None)
            return None

        log_message("INFO", "自动计算", f"开始分析{HISTORICAL_ANALYSIS_YEARS}年历史数据...", None, ContextInfo)

        # 1. 获取历史数据
        end_date = get_current_time(ContextInfo)
        start_date = end_date - datetime.timedelta(days=HISTORICAL_ANALYSIS_YEARS * 365)

        # 使用现有的API获取历史数据
        historical_data = get_historical_data_for_analysis(
            SIGNAL_FUND_CODE,
            start_date.strftime('%Y%m%d'),
            end_date.strftime('%Y%m%d'),
            ContextInfo
        )

        if historical_data is None or len(historical_data) == 0:
            log_message("ERROR", "自动计算", "无法获取历史数据", None, ContextInfo)
            return None

        # 2. 重采样为季度数据
        quarterly_data = resample_daily_to_period(historical_data, EMA_DETECTION_CYCLE)
        if quarterly_data is None or len(quarterly_data) < EMA_PERIOD + 2:
            log_message("ERROR", "自动计算", "历史数据不足以计算EMA", None, ContextInfo)
            return None

        # 3. 计算技术指标
        close_prices = quarterly_data['close'].values if hasattr(quarterly_data['close'], 'values') else list(quarterly_data['close'])
        ema_values = calculate_ema(close_prices, EMA_PERIOD)

        if len(ema_values) < 2:
            log_message("ERROR", "自动计算", "EMA计算结果不足", None, ContextInfo)
            return None

        # 4. 查找买入信号
        activation_points = []

        for i in range(1, len(close_prices)):
            current_close = close_prices[i]
            previous_close = close_prices[i-1]
            current_ema = ema_values[i]
            previous_ema = ema_values[i-1]

            current_bottom_line = current_ema * BOTTOM_RATIO
            previous_bottom_line = previous_ema * BOTTOM_RATIO

            # 检测买入信号：收盘价向下跌破底部线
            if (previous_close >= previous_bottom_line and
                current_close < current_bottom_line):

                # 获取对应的日期
                if hasattr(quarterly_data, 'index'):
                    signal_date = quarterly_data.index[i]
                else:
                    signal_date = quarterly_data['index'][i]

                activation_points.append({
                    'date': signal_date.strftime('%Y-%m-%d') if hasattr(signal_date, 'strftime') else str(signal_date)[:10],
                    'price': current_close,
                    'ema': current_ema,
                    'bottom_line': current_bottom_line,
                    'index': i
                })

        if not activation_points:
            log_message("WARNING", "自动计算", "未找到历史买入信号，使用默认值", None, ContextInfo)
            return None

        # 5. 选择最近的激活点
        latest_point = activation_points[-1]  # 最后一个（最近的）激活点

        log_message("INFO", "自动计算",
                   f"找到{len(activation_points)}个历史买入信号，"
                   f"最近的激活点：{latest_point['date']}, "
                   f"价格：{latest_point['price']:.4f}", None, ContextInfo)

        return {
            'date': latest_point['date'],
            'price': latest_point['price'],
            'ema': latest_point['ema'],
            'bottom_line': latest_point['bottom_line'],
            'source': 'auto_calculated',
            'total_signals': len(activation_points)
        }

    except Exception as e:
        error_msg = f"自动计算激活点失败：{str(e)}"
        log_message("ERROR", "自动计算", error_msg, None, ContextInfo)
        return None


def get_historical_data_for_analysis(stock_code: str, start_date: str, end_date: str, ContextInfo=None):
    """
    获取用于分析的历史数据
    直接使用iQuant的get_market_data_ex API

    Args:
        stock_code: 股票代码
        start_date: 开始日期 (YYYYMMDD)
        end_date: 结束日期 (YYYYMMDD)
        ContextInfo: iQuant上下文信息对象

    Returns:
        历史数据或None
    """
    try:
        if ContextInfo is None:
            log_message("ERROR", "数据获取", "ContextInfo为空，无法获取历史数据", None)
            return None

        log_message("INFO", "数据获取", f"获取{stock_code}从{start_date}到{end_date}的历史数据", None, ContextInfo)

        # 直接使用现有的API获取历史数据
        market_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close'],
            stock_code=[stock_code],
            period='1d',  # 日线数据
            start_time=start_date,
            end_time=end_date,
            dividend_type='front',  # 前复权
            fill_data=True
        )

        if market_data is None or len(market_data) == 0:
            log_message("WARNING", "数据获取", f"无法获取{stock_code}的历史数据", None, ContextInfo)
            return None

        # 获取具体股票的数据
        stock_data = market_data.get(stock_code)
        if stock_data is None or len(stock_data) == 0:
            log_message("WARNING", "数据获取", f"无法获取{stock_code}的历史数据", None, ContextInfo)
            return None

        log_message("INFO", "数据获取", f"成功获取{stock_code}的{len(stock_data)}条历史数据记录", None, ContextInfo)
        return stock_data

    except Exception as e:
        log_message("ERROR", "数据获取", f"获取历史数据失败：{str(e)}", None, ContextInfo)
        return None


def insert_simulated_buy_signal_with_info(activation_info: dict, ContextInfo=None):
    """
    根据激活点信息插入模拟的买入信号记录

    Args:
        activation_info: 激活点信息字典
        ContextInfo: iQuant上下文信息对象
    """
    try:
        cursor = g_db_connection.cursor()

        signal_date = activation_info['date']
        signal_price = activation_info['price']

        # 检查是否已存在该日期的买入信号
        cursor.execute("""
            SELECT COUNT(*) FROM signal_history
            WHERE signal_type = 'ENTERLONG'
            AND DATE(signal_date) = ?
            AND is_valid = 1
        """, (signal_date,))

        if cursor.fetchone()[0] > 0:
            log_message("INFO", "模拟信号", f"{signal_date}的买入信号已存在，跳过插入", None, ContextInfo)
            return True

        # 计算技术指标值
        if 'ema' in activation_info and 'bottom_line' in activation_info:
            simulated_ema = activation_info['ema']
            simulated_bottom_line = activation_info['bottom_line']
        else:
            # 反推技术指标值
            simulated_ema = signal_price / BOTTOM_RATIO
            simulated_bottom_line = simulated_ema * BOTTOM_RATIO

        signal_time = signal_date + " 15:00:00"
        current_time = get_current_time_str(ContextInfo)

        # 插入模拟买入信号
        cursor.execute("""
            INSERT INTO signal_history
            (signal_date, signal_type, signal_price, ema_value, bottom_line, top_line,
             kline_position, kline_date, is_valid, filter_reason, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            signal_time,
            'ENTERLONG',
            signal_price,
            simulated_ema,
            simulated_bottom_line,
            None,  # top_line
            9999,  # 模拟的kline_position
            signal_date.replace('-', ''),  # kline_date (YYYYMMDD格式)
            1,     # is_valid
            f"模拟信号({activation_info['source']})",  # filter_reason
            current_time
        ))

        g_db_connection.commit()

        log_message("INFO", "模拟信号",
                   f"已插入模拟买入信号 - 日期：{signal_time}, "
                   f"价格：{signal_price:.4f}, EMA：{simulated_ema:.4f}, "
                   f"来源：{activation_info['source']}", None, ContextInfo)

        return True

    except Exception as e:
        error_msg = f"插入模拟买入信号失败：{str(e)}"
        log_message("ERROR", "模拟信号", error_msg, None, ContextInfo)
        return False


def get_strategy_performance_summary() -> Dict:
    """
    获取策略表现摘要

    Returns:
        dict: 表现摘要
    """
    try:
        summary = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_signals': 0,
            'valid_signals': 0,
            'filtered_signals': 0,
            'current_phase': 'unknown',
            'phase_duration_days': 0
        }

        if g_strategy_status:
            summary['current_phase'] = g_strategy_status.get('current_phase', 'unknown')
            summary['phase_duration_days'] = get_strategy_phase_duration()

        cursor = g_db_connection.cursor()

        # 统计交易次数
        cursor.execute("SELECT COUNT(*) FROM trade_orders")
        result = cursor.fetchone()
        if result:
            summary['total_trades'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM trade_orders WHERE order_status = 'SUCCESS'")
        result = cursor.fetchone()
        if result:
            summary['successful_trades'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM trade_orders WHERE order_status = 'FAILED'")
        result = cursor.fetchone()
        if result:
            summary['failed_trades'] = result[0]

        # 统计信号次数
        cursor.execute("SELECT COUNT(*) FROM signal_history")
        result = cursor.fetchone()
        if result:
            summary['total_signals'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM signal_history WHERE is_valid = 1")
        result = cursor.fetchone()
        if result:
            summary['valid_signals'] = result[0]

        cursor.execute("SELECT COUNT(*) FROM signal_history WHERE is_valid = 0")
        result = cursor.fetchone()
        if result:
            summary['filtered_signals'] = result[0]

        return summary

    except Exception as e:
        log_message("ERROR", "状态管理", f"获取策略表现摘要失败：{str(e)}", None)
        return {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_signals': 0,
            'valid_signals': 0,
            'filtered_signals': 0,
            'current_phase': 'unknown',
            'phase_duration_days': 0
        }


# ==================== 交易任务队列系统 ====================

import uuid
import json
from typing import List, Dict, Optional, Tuple
from enum import Enum

# 任务状态枚举
class TaskStatus(Enum):
    PENDING = "PENDING"                    # 待执行
    EXECUTING = "EXECUTING"                # 执行中
    WAITING_CALLBACK = "WAITING_CALLBACK"  # 等待回调
    COMPLETED = "COMPLETED"                # 已完成
    FAILED = "FAILED"                      # 失败
    TIMEOUT = "TIMEOUT"                    # 超时

# 任务类型枚举
class TaskType(Enum):
    SELL_510720 = "SELL_510720"           # 卖出510720
    SELL_159915 = "SELL_159915"           # 卖出159915
    BUY_159915_CASH = "BUY_159915_CASH"   # 用现金买入159915
    BUY_159915_MARGIN = "BUY_159915_MARGIN" # 用融资买入159915
    BUY_510720 = "BUY_510720"             # 买入510720

# 快照时点枚举
class SnapshotPoint(Enum):
    BEFORE_SELL = "BEFORE_SELL"           # 卖出前
    AFTER_SELL = "AFTER_SELL"             # 卖出后
    BEFORE_BUY = "BEFORE_BUY"             # 买入前
    AFTER_BUY_CASH = "AFTER_BUY_CASH"     # 现金买入后
    AFTER_BUY_MARGIN = "AFTER_BUY_MARGIN" # 融资买入后

# 订单状态映射（基于iQuant文档）
ORDER_STATUS_MAP = {
    56: 'COMPLETED',    # 已成
    54: 'CANCELLED',    # 已撤
    57: 'FAILED',       # 废单
    # 可以根据需要添加更多状态码
}

class TradeTaskQueue:
    """交易任务队列管理器"""

    def __init__(self):
        self.timeout_levels = {
            'WARNING': 60,      # 1分钟：记录警告
            'QUERY': 300,       # 5分钟：主动查询状态
            'ALERT': 1800,      # 30分钟：发送告警
        }

    def create_task_group(self, stock_code: str, target_shares: int, order_reason: str) -> str:
        """
        创建交易任务组

        Args:
            stock_code: 目标股票代码
            target_shares: 目标买入股数
            order_reason: 交易原因

        Returns:
            str: 任务组ID
        """
        task_group_id = str(uuid.uuid4())
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            # 记录任务组创建日志
            self.log_task_message(
                task_group_id=task_group_id,
                level="INFO",
                category="TASK_CREATE",
                message=f"创建交易任务组：目标买入{stock_code} {target_shares}股，原因：{order_reason}"
            )

            return task_group_id

        except Exception as e:
            error_msg = f"创建任务组失败：{str(e)}"
            print(error_msg)
            self.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="TASK_CREATE",
                message=error_msg
            )
            raise

    def log_task_message(self, task_group_id: str = None, task_id: int = None,
                        level: str = "INFO", category: str = "GENERAL",
                        message: str = "", extra_data: Dict = None):
        """
        记录任务日志

        Args:
            task_group_id: 任务组ID
            task_id: 任务ID
            level: 日志级别
            category: 日志分类
            message: 日志消息
            extra_data: 额外数据
        """
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute("""
                INSERT INTO trade_task_log
                (task_id, task_group_id, log_level, log_category, log_message, extra_data, log_time)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                task_id,
                task_group_id,
                level,
                category,
                message,
                json.dumps(extra_data) if extra_data else None,
                current_time
            ))

            g_db_connection.commit()

            # 同时记录到主日志系统
            log_message(level, f"任务队列-{category}", message, extra_data, None)

        except Exception as e:
            print(f"记录任务日志失败：{str(e)}")

    def create_account_snapshot(self, task_group_id: str, snapshot_point: str, ContextInfo):
        """
        创建账户快照

        Args:
            task_group_id: 任务组ID
            snapshot_point: 快照时点
            ContextInfo: iQuant上下文信息
        """
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 获取账户信息
            account_info = get_account_info(ContextInfo)

            position_510720 = get_current_position(SLEEPING_FUND_CODE)
            position_159915 = get_current_position(ACTIVE_FUND_CODE)

            # 获取融资信息（如果可用）
            margin_available = account_info.get('credit_available', 0)

            cursor = g_db_connection.cursor()
            cursor.execute("""
                INSERT INTO account_snapshot
                (task_group_id, snapshot_point, available_cash, margin_available,
                 stock_510720_shares, stock_510720_value, stock_159915_shares, stock_159915_value,
                 snapshot_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_group_id,
                snapshot_point,
                account_info['available_cash'],
                margin_available,
                position_510720['shares'],
                position_510720['market_value'],
                position_159915['shares'],
                position_159915['market_value'],
                current_time
            ))

            g_db_connection.commit()

            self.log_task_message(
                task_group_id=task_group_id,
                level="INFO",
                category="SNAPSHOT",
                message=f"创建账户快照：{snapshot_point}",
                extra_data={
                    'available_cash': account_info['available_cash'],
                    'margin_available': margin_available,
                    'stock_510720_shares': position_510720['shares'],
                    'stock_159915_shares': position_159915['shares']
                }
            )

        except Exception as e:
            error_msg = f"创建账户快照失败：{str(e)}"
            self.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="SNAPSHOT",
                message=error_msg
            )

    def calculate_fees(self, amount: float, is_sell: bool = False) -> float:
        """
        计算交易费用

        Args:
            amount: 交易金额
            is_sell: 是否为卖出交易

        Returns:
            float: 总费用
        """
        # 手续费
        commission = max(amount * COMMISSION_FEE_RATE, COMMISSION_FEE_MIN)

        # 印花税（仅卖出时收取）
        stamp_tax = amount * SELL_TAX_RATE if is_sell else 0

        return commission + stamp_tax

    def create_task(self, task_group_id: str, task_type: str, stock_code: str,
                   target_shares: int, target_amount: float = None,
                   depends_on_task: str = None, task_params: Dict = None, ContextInfo=None) -> tuple:
        """
        创建单个交易任务

        Args:
            task_group_id: 任务组ID
            task_type: 任务类型
            stock_code: 股票代码
            target_shares: 目标股数
            target_amount: 目标金额
            depends_on_task: 依赖的任务ID
            task_params: 任务参数
            ContextInfo: iQuant上下文信息对象（用于获取实时价格）

        Returns:
            tuple: (task_id, order_uuid)
        """
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 生成订单UUID用于回调匹配
            order_uuid = str(uuid.uuid4())

            # 估算价格和费用
            estimated_price = get_current_price(stock_code, ContextInfo)  # 使用当前价格估算
            if target_amount is None:
                target_amount = target_shares * estimated_price

            estimated_fees = self.calculate_fees(target_amount, task_type == TaskType.SELL_510720.value)

            cursor = g_db_connection.cursor()
            cursor.execute("""
                INSERT INTO trade_task_queue
                (task_group_id, task_type, stock_code, target_shares, target_amount,
                 estimated_price, estimated_fees, task_status, depends_on_task, order_uuid, task_params, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_group_id,
                task_type,
                stock_code,
                target_shares,
                target_amount,
                estimated_price,
                estimated_fees,
                TaskStatus.PENDING.value,
                depends_on_task,
                order_uuid,
                json.dumps(task_params) if task_params else None,
                current_time
            ))

            task_id = cursor.lastrowid
            g_db_connection.commit()

            self.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="INFO",
                category="TASK_CREATE",
                message=f"创建任务：{task_type} {stock_code} {target_shares}股，订单UUID：{order_uuid}",
                extra_data={
                    'task_type': task_type,
                    'stock_code': stock_code,
                    'target_shares': target_shares,
                    'target_amount': target_amount,
                    'estimated_price': estimated_price,
                    'estimated_fees': estimated_fees,
                    'depends_on_task': depends_on_task,
                    'order_uuid': order_uuid
                }
            )

            return task_id, order_uuid

        except Exception as e:
            error_msg = f"创建任务失败：{str(e)}"
            self.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="TASK_CREATE",
                message=error_msg
            )
            raise

# ==================== 费用计算函数 ====================

def calculate_trading_fees(amount: float, shares: int, trade_type: str, stock_code: str = None) -> dict:
    """计算交易费用（全局函数）"""
    # 佣金（买卖都收取）
    commission = max(amount * COMMISSION_FEE_RATE, COMMISSION_FEE_MIN)

    # 印花税（仅卖出时收取）
    stamp_tax = amount * SELL_TAX_RATE if trade_type == 'SELL' else 0.0

    # 过户费（根据股票代码判断）
    transfer_fee = 0.0
    if stock_code:
        # 上海股票（以6开头或者.SH结尾）收取过户费
        if stock_code.startswith('6') or stock_code.endswith('.SH'):
            transfer_fee = max(amount * TRANSFER_FEE_RATE, 1.0)  # 最低1元
        # 深圳股票（以0、2、3开头或者.SZ结尾）免收过户费
        elif stock_code.startswith(('0', '2', '3')) or stock_code.endswith('.SZ'):
            transfer_fee = 0.0

    total_fees = commission + stamp_tax + transfer_fee

    # 计算净金额
    if trade_type == 'BUY':
        net_amount = -(amount + total_fees)  # 买入总支出
    else:
        net_amount = amount - total_fees     # 卖出净收入

    return {
        'commission': commission,
        'stamp_tax': stamp_tax,
        'transfer_fee': transfer_fee,
        'total_fees': total_fees,
        'net_amount': net_amount,
        'gross_amount': amount
    }

# 全局任务队列管理器实例
g_trade_task_queue = TradeTaskQueue()


def execute_active_period_trade_async(trade_type: str, target_shares: int, order_reason: str, ContextInfo) -> str:
    """
    激活期交易异步执行函数

    适用场景：
    1. 激活期的定期投资（价值平均策略）
    2. 激活期内的仓位调整

    交易逻辑：
    - BUY: 卖出部分510720获取现金 → 买入159915 → 如需要则融资买入
    - SELL: 卖出159915 → 买入510720

    Args:
        trade_type: 交易类型（'BUY' 或 'SELL'）
        target_shares: 目标交易的159915股数
        order_reason: 交易原因（VALUE_AVERAGE, PERIOD_INVESTMENT等）
        ContextInfo: iQuant上下文信息对象

    Returns:
        str: 任务组ID
    """
    try:
        # 1. 创建任务组
        task_group_id = g_trade_task_queue.create_task_group(ACTIVE_FUND_CODE, target_shares, f"{trade_type}_{order_reason}")

        # 2. 创建初始账户快照
        snapshot_type = "BEFORE_BUY" if trade_type == "BUY" else "BEFORE_SELL"
        g_trade_task_queue.create_account_snapshot(task_group_id, snapshot_type, ContextInfo)

        if trade_type == "BUY":
            # 买入159915的逻辑
            return _execute_buy_159915_logic(task_group_id, target_shares, order_reason, ContextInfo)
        elif trade_type == "SELL":
            # 卖出159915的逻辑
            return _execute_sell_159915_logic(task_group_id, target_shares, order_reason, ContextInfo)
        else:
            raise ValueError(f"不支持的交易类型：{trade_type}")

    except Exception as e:
        error_msg = f"创建激活期交易任务组失败：{str(e)}"
        print(error_msg)
        if 'task_group_id' in locals():
            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="TASK_CREATE",
                message=error_msg
            )
        return ""


def _execute_buy_159915_logic(task_group_id: str, target_shares: int, order_reason: str, ContextInfo) -> str:
    """执行买入159915的逻辑"""
    try:

        # 3. 计算159915需要买入的份额和预计金额
        current_price_159915 = get_current_price(ACTIVE_FUND_CODE, ContextInfo)
        target_amount = target_shares * current_price_159915
        estimated_fees_buy = g_trade_task_queue.calculate_fees(target_amount, False)
        total_needed = target_amount + estimated_fees_buy

        g_trade_task_queue.log_task_message(
            task_group_id=task_group_id,
            level="INFO",
            category="CALCULATION",
            message=f"计算买入需求：{target_shares}股 {ACTIVE_FUND_CODE}，预计金额：{target_amount:.2f}，预计费用：{estimated_fees_buy:.2f}，总需求：{total_needed:.2f}",
            extra_data={
                'target_shares': target_shares,
                'current_price': current_price_159915,
                'target_amount': target_amount,
                'estimated_fees': estimated_fees_buy,
                'total_needed': total_needed
            }
        )

        # 4. 查询510720的可用份额和最新价，计算卖出后预计现金
        position_510720 = get_current_position(SLEEPING_FUND_CODE)
        current_price_510720 = get_current_price(SLEEPING_FUND_CODE, ContextInfo)

        if position_510720['shares'] > 0:
            # 计算全部卖出510720能得到的现金
            total_sell_amount = position_510720['shares'] * current_price_510720
            estimated_fees_sell = g_trade_task_queue.calculate_fees(total_sell_amount, True)
            net_cash_from_sell = total_sell_amount - estimated_fees_sell

            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="INFO",
                category="CALCULATION",
                message=f"510720持仓分析：{position_510720['shares']}股，当前价：{current_price_510720:.4f}，全部卖出可得现金：{net_cash_from_sell:.2f}",
                extra_data={
                    'shares_510720': position_510720['shares'],
                    'price_510720': current_price_510720,
                    'total_sell_amount': total_sell_amount,
                    'estimated_fees_sell': estimated_fees_sell,
                    'net_cash_from_sell': net_cash_from_sell
                }
            )

            # 判断是否需要全部卖出还是部分卖出
            if net_cash_from_sell >= total_needed:
                # 计算需要卖出的份额（部分卖出，必须是100的倍数）
                needed_sell_amount = total_needed / (1 - COMMISSION_FEE_RATE - SELL_TAX_RATE)  # 考虑费用反推
                ideal_shares = int(needed_sell_amount / current_price_510720) + 1
                # 确保卖出股数是MIN_TRADE_SHARES的整数倍
                shares_to_sell_rounded = int((ideal_shares + MIN_TRADE_SHARES - 1) / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
                shares_to_sell = min(shares_to_sell_rounded, position_510720['shares'])
            else:
                # 全部卖出
                shares_to_sell = position_510720['shares']

            # 创建卖出510720任务
            sell_task_id, sell_order_uuid = g_trade_task_queue.create_task(
                task_group_id=task_group_id,
                task_type=TaskType.SELL_510720.value,
                stock_code=SLEEPING_FUND_CODE,
                target_shares=shares_to_sell,
                task_params={'reason': f'为买入{ACTIVE_FUND_CODE}腾出资金'},
                ContextInfo=ContextInfo
            )

        else:
            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="INFO",
                category="CALCULATION",
                message="510720无持仓，跳过卖出步骤"
            )
            sell_task_id = None

        # 5. 创建买入159915的任务（现金部分）
        buy_cash_task_id, buy_order_uuid = g_trade_task_queue.create_task(
            task_group_id=task_group_id,
            task_type=TaskType.BUY_159915_CASH.value,
            stock_code=ACTIVE_FUND_CODE,
            target_shares=target_shares,  # 先尝试用现金买入全部
            depends_on_task=str(sell_task_id) if sell_task_id else None,
            task_params={'reason': order_reason, 'original_target_shares': target_shares},
            ContextInfo=ContextInfo
        )

        # 6. 智能判断是否需要创建融资任务
        # 估算现金是否足够买入全部股数
        account_info = get_account_info(ContextInfo)
        available_cash = account_info.get('available_cash', 0)
        current_price = get_current_price(ACTIVE_FUND_CODE, ContextInfo)
        estimated_cost = target_shares * current_price * 1.005  # 加0.5%缓冲，更保守

        buy_margin_task_id = None
        if estimated_cost > available_cash:
            # 现金可能不够，创建融资备用任务
            buy_margin_task_id, buy_order_uuid = g_trade_task_queue.create_task(
                task_group_id=task_group_id,
                task_type=TaskType.BUY_159915_MARGIN.value,
                stock_code=ACTIVE_FUND_CODE,
                target_shares=0,  # 初始为0，实际执行时根据现金买入结果确定
                depends_on_task=str(buy_cash_task_id),
                task_params={'reason': order_reason, 'buy_remaining': True, 'original_target_shares': target_shares},
                ContextInfo=ContextInfo
            )

            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="INFO",
                category="TASK_CREATE",
                message=f"现金可能不足（需要{estimated_cost:.2f}，可用{available_cash:.2f}），创建融资备用任务",
                extra_data={
                    'estimated_cost': estimated_cost,
                    'available_cash': available_cash,
                    'margin_task_created': True
                }
            )
        else:
            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="INFO",
                category="TASK_CREATE",
                message=f"现金充足（需要{estimated_cost:.2f}，可用{available_cash:.2f}），无需创建融资任务",
                extra_data={
                    'estimated_cost': estimated_cost,
                    'available_cash': available_cash,
                    'margin_task_created': False
                }
            )

        # 计算实际创建的任务数量
        task_count = 1  # 至少有现金买入任务
        if sell_task_id:
            task_count += 1
        if buy_margin_task_id:
            task_count += 1

        g_trade_task_queue.log_task_message(
            task_group_id=task_group_id,
            level="INFO",
            category="TASK_CREATE",
            message=f"任务组创建完成，包含{task_count}个任务",
            extra_data={
                'sell_task_id': sell_task_id,
                'buy_cash_task_id': buy_cash_task_id,
                'buy_margin_task_id': buy_margin_task_id,
                'margin_task_needed': buy_margin_task_id is not None
            }
        )

        return task_group_id

    except Exception as e:
        error_msg = f"创建买入159915任务组失败：{str(e)}"
        print(error_msg)
        if 'task_group_id' in locals():
            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="TASK_CREATE",
                message=error_msg
            )
        raise


def _execute_sell_159915_logic(task_group_id: str, target_shares: int, order_reason: str, ContextInfo) -> str:
    """
    执行卖出159915的逻辑

    修改说明：激活期内卖出159915后，现金保留在账户中，不再自动买入510720
    """
    try:
        # 1. 检查159915持仓
        position_159915 = get_current_position(ACTIVE_FUND_CODE)
        current_price_159915 = get_current_price(ACTIVE_FUND_CODE, ContextInfo)

        if position_159915['shares'] < target_shares:
            error_msg = f"159915持仓不足：持有{position_159915['shares']}股，需要卖出{target_shares}股"
            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="VALIDATION",
                message=error_msg
            )
            raise ValueError(error_msg)

        # 2. 计算卖出159915能得到的现金
        sell_amount = target_shares * current_price_159915
        estimated_fees_sell = g_trade_task_queue.calculate_fees(sell_amount, True)
        net_cash_from_sell = sell_amount - estimated_fees_sell

        g_trade_task_queue.log_task_message(
            task_group_id=task_group_id,
            level="INFO",
            category="CALCULATION",
            message=f"激活期内卖出159915：{target_shares}股，当前价：{current_price_159915:.4f}，预计得到现金：{net_cash_from_sell:.2f}（现金将保留在账户中）",
            extra_data={
                'target_shares': target_shares,
                'current_price': current_price_159915,
                'sell_amount': sell_amount,
                'estimated_fees': estimated_fees_sell,
                'net_cash': net_cash_from_sell,
                'note': '激活期内卖出后现金保留，不买入510720'
            }
        )

        # 3. 创建卖出159915任务（激活期内只卖出，不买入510720）
        sell_task_id, sell_order_uuid = g_trade_task_queue.create_task(
            task_group_id=task_group_id,
            task_type=TaskType.SELL_159915.value,
            stock_code=ACTIVE_FUND_CODE,
            target_shares=target_shares,
            task_params={'reason': order_reason, 'keep_cash': True},
            ContextInfo=ContextInfo
        )

        g_trade_task_queue.log_task_message(
            task_group_id=task_group_id,
            level="INFO",
            category="TASK_CREATE",
            message=f"激活期卖出159915任务创建完成：卖出{target_shares}股159915，现金保留在账户中",
            extra_data={
                'sell_task_id': sell_task_id,
                'target_shares': target_shares,
                'estimated_cash': net_cash_from_sell,
                'strategy': 'ACTIVE_PERIOD_SELL_ONLY'
            }
        )

        return task_group_id

    except Exception as e:
        error_msg = f"创建激活期卖出159915任务组失败：{str(e)}"
        print(error_msg)
        if 'task_group_id' in locals():
            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="TASK_CREATE",
                message=error_msg
            )
        raise


def execute_active_to_sleeping_transition_async(ContextInfo, signal_details: Dict) -> str:
    """
    激活期到沉睡期转换异步执行函数

    适用场景：
    - 检测到卖出信号，需要从激活期切换到沉睡期

    交易逻辑（修改后）：
    - 卖出所有159915持仓
    - 按159915市值计算能转换的510720份额
    - 检查账户现金是否充足买入转换份额
    - 如果充足，按转换份额买入；如果不足，用尽现金买入

    Args:
        ContextInfo: iQuant上下文信息对象
        signal_details: 信号详情（包含卖出原因等）

    Returns:
        str: 任务组ID
    """
    try:
        # 1. 创建任务组
        order_reason = f"PHASE_TRANSITION_{signal_details.get('signal_type', 'UNKNOWN')}"
        task_group_id = g_trade_task_queue.create_task_group(ACTIVE_FUND_CODE, 0, order_reason)

        # 2. 创建初始账户快照（转换前）
        g_trade_task_queue.create_account_snapshot(task_group_id, "BEFORE_TRANSITION", ContextInfo)

        # 3. 查询159915的当前持仓和510720价格
        position_159915 = get_current_position(ACTIVE_FUND_CODE)
        current_price_159915 = get_current_price(ACTIVE_FUND_CODE, ContextInfo)
        current_price_510720 = get_current_price(SLEEPING_FUND_CODE, ContextInfo)

        if position_159915['shares'] > 0:
            # 4. 计算159915市值（按当前价格）
            market_value_159915 = position_159915['shares'] * current_price_159915

            # 5. 计算能转换的510720份额（按市值转换）
            target_510720_shares_by_value = int(market_value_159915 / current_price_510720 / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
            target_510720_amount = target_510720_shares_by_value * current_price_510720

            # 6. 获取账户现金信息
            account_info = get_account_info(ContextInfo)
            available_cash = account_info.get('available_cash', 0) if account_info else 0

            # 7. 计算买入510720的费用
            estimated_fees_buy = g_trade_task_queue.calculate_fees(target_510720_amount, False)
            total_needed_cash = target_510720_amount + estimated_fees_buy

            # 8. 决定买入策略
            if available_cash >= total_needed_cash:
                # 现金充足，按转换份额买入
                final_510720_shares = target_510720_shares_by_value
                buy_strategy = "CONVERT_BY_SHARES"
                strategy_note = f"现金充足（{available_cash:.2f}≥{total_needed_cash:.2f}），按转换份额买入"
            else:
                # 现金不足，用尽现金买入
                final_510720_shares = 0  # 实际执行时根据可用现金确定
                buy_strategy = "USE_ALL_CASH"
                strategy_note = f"现金不足（{available_cash:.2f}<{total_needed_cash:.2f}），用尽现金买入"

            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="INFO",
                category="CALCULATION",
                message=f"阶段转换计算：159915持仓{position_159915['shares']}股（市值{market_value_159915:.2f}），转换目标{target_510720_shares_by_value}股510720，{strategy_note}",
                extra_data={
                    'shares_159915': position_159915['shares'],
                    'price_159915': current_price_159915,
                    'market_value_159915': market_value_159915,
                    'price_510720': current_price_510720,
                    'target_510720_shares': target_510720_shares_by_value,
                    'target_510720_amount': target_510720_amount,
                    'available_cash': available_cash,
                    'total_needed_cash': total_needed_cash,
                    'buy_strategy': buy_strategy,
                    'final_510720_shares': final_510720_shares,
                    'signal_details': signal_details
                }
            )

            # 9. 创建卖出159915任务
            sell_task_id, sell_order_uuid = g_trade_task_queue.create_task(
                task_group_id=task_group_id,
                task_type=TaskType.SELL_159915.value,
                stock_code=ACTIVE_FUND_CODE,
                target_shares=position_159915['shares'],
                task_params={'reason': order_reason, 'signal_details': signal_details},
                ContextInfo=ContextInfo
            )

            # 10. 创建买入510720任务（依赖于卖出完成）
            buy_task_params = {
                'reason': order_reason,
                'buy_strategy': buy_strategy,
                'target_shares_by_value': target_510720_shares_by_value
            }

            if buy_strategy == "USE_ALL_CASH":
                buy_task_params['use_all_cash'] = True

            buy_task_id, buy_order_uuid = g_trade_task_queue.create_task(
                task_group_id=task_group_id,
                task_type=TaskType.BUY_510720.value,
                stock_code=SLEEPING_FUND_CODE,
                target_shares=final_510720_shares,
                depends_on_task=str(sell_task_id),
                task_params=buy_task_params,
                ContextInfo=ContextInfo
            )

            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="INFO",
                category="TASK_CREATE",
                message=f"阶段转换任务组创建完成：卖出{position_159915['shares']}股159915，{buy_strategy}买入510720",
                extra_data={
                    'sell_task_id': sell_task_id,
                    'buy_task_id': buy_task_id,
                    'buy_strategy': buy_strategy,
                    'target_510720_shares': final_510720_shares,
                    'transition_type': 'ACTIVE_TO_SLEEPING_BY_VALUE'
                }
            )

        else:
            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="WARNING",
                category="CALCULATION",
                message="159915无持仓，无需卖出，直接完成阶段转换"
            )

        return task_group_id

    except Exception as e:
        error_msg = f"创建阶段转换任务组失败：{str(e)}"
        print(error_msg)
        if 'task_group_id' in locals():
            g_trade_task_queue.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="TASK_CREATE",
                message=error_msg
            )
        return ""


class TradeTaskExecutor:
    """交易任务执行器"""

    def __init__(self):
        self.task_queue = g_trade_task_queue

    def process_pending_tasks(self, ContextInfo):
        """
        处理待执行的任务

        Args:
            ContextInfo: iQuant上下文信息对象
        """
        try:
            # 1. 检查超时任务
            self.check_timeout_tasks()

            # 2. 查找下一个可执行的任务
            next_task = self.get_next_executable_task()

            if next_task:
                self.execute_task(next_task, ContextInfo)

        except Exception as e:
            error_msg = f"处理任务队列失败：{str(e)}"
            print(error_msg)
            log_message("ERROR", "任务执行器", error_msg, None, ContextInfo)

    def get_next_executable_task(self) -> Optional[Dict]:
        """
        获取下一个可执行的任务

        Returns:
            Dict: 任务信息，如果没有可执行任务则返回None
        """
        try:
            cursor = g_db_connection.cursor()

            # 查找状态为PENDING且依赖任务已完成的任务
            cursor.execute("""
                SELECT t1.* FROM trade_task_queue t1
                WHERE t1.task_status = 'PENDING'
                AND (
                    t1.depends_on_task IS NULL
                    OR EXISTS (
                        SELECT 1 FROM trade_task_queue t2
                        WHERE t2.id = CAST(t1.depends_on_task AS INTEGER)
                        AND t2.task_status = 'COMPLETED'
                    )
                )
                ORDER BY t1.created_time ASC
                LIMIT 1
            """)

            result = cursor.fetchone()
            if result:
                # 将结果转换为字典
                columns = [desc[0] for desc in cursor.description]
                row_dict = dict(zip(columns, result))
                # 将task_param从JSON字符串转换为dict对象
                if 'task_params' in row_dict and row_dict['task_params']:
                    try:
                        row_dict['task_params'] = json.loads(row_dict['task_params'])
                    except (json.JSONDecodeError, TypeError):
                        # 如果JSON解析失败，保持原值或设为None
                        row_dict['task_params'] = None
                return row_dict
            return None

        except Exception as e:
            error_msg = f"查找可执行任务失败：{str(e)}"
            print(error_msg)
            log_message("ERROR", "任务执行器", error_msg, None)
            return None

    def execute_task(self, task: Dict, ContextInfo):
        """
        执行单个任务

        Args:
            task: 任务信息
            ContextInfo: iQuant上下文信息对象
        """
        task_id = task['id']
        task_type = task['task_type']

        try:
            # 更新任务状态为执行中
            self.update_task_status(task_id, TaskStatus.EXECUTING.value)

            self.task_queue.log_task_message(
                task_group_id=task['task_group_id'],
                task_id=task_id,
                level="INFO",
                category="TASK_EXECUTE",
                message=f"开始执行任务：{task_type} {task['stock_code']} {task['target_shares']}股"
            )

            # 根据任务类型执行相应操作
            if task_type == TaskType.SELL_510720.value:
                self.execute_sell_task(task, ContextInfo)
            elif task_type == TaskType.SELL_159915.value:
                self.execute_sell_task(task, ContextInfo)  # 卖出逻辑通用
            elif task_type == TaskType.BUY_159915_CASH.value:
                self.execute_buy_cash_task(task, ContextInfo)
            elif task_type == TaskType.BUY_159915_MARGIN.value:
                self.execute_buy_margin_task(task, ContextInfo)
            elif task_type == TaskType.BUY_510720.value:
                self.execute_buy_cash_task(task, ContextInfo)  # 买入510720使用现金买入逻辑
            else:
                raise ValueError(f"未知任务类型：{task_type}")

        except Exception as e:
            
            error_msg = f"执行任务失败：{str(e)}"
            
            self.task_queue.log_task_message(
                task_group_id=task['task_group_id'],
                task_id=task_id,
                level="ERROR",
                category="TASK_EXECUTE",
                message=error_msg
            )
            self.update_task_status(task_id, TaskStatus.FAILED.value, error_msg)

    def update_task_status(self, task_id: int, status: str, error_message: str = None):
        """
        更新任务状态

        Args:
            task_id: 任务ID
            status: 新状态
            error_message: 错误信息
        """
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            if status == TaskStatus.EXECUTING.value:
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = ?, started_time = ?
                    WHERE id = ?
                """, (status, current_time, task_id))
            elif status in [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value, TaskStatus.TIMEOUT.value]:
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = ?, completed_time = ?, error_message = ?
                    WHERE id = ?
                """, (status, current_time, error_message, task_id))
            else:
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = ?, error_message = ?
                    WHERE id = ?
                """, (status, error_message, task_id))

            g_db_connection.commit()

        except Exception as e:
            error_msg = f"更新任务状态失败：{str(e)}"
            print(error_msg)
            log_message("ERROR", "任务执行器", error_msg, None)

    def execute_sell_task(self, task: Dict, ContextInfo):
        """
        执行卖出任务

        Args:
            task: 任务信息
            ContextInfo: iQuant上下文信息对象
        """
        task_id = task['id']
        stock_code = task['stock_code']
        target_shares = task['target_shares']
        order_uuid = task['order_uuid']

        try:
            # 检查当前持仓
            position = get_current_position(stock_code)
            if position['shares'] < target_shares:
                # 调整为实际可卖出股数
                actual_shares = position['shares']
                self.task_queue.log_task_message(
                    task_group_id=task['task_group_id'],
                    task_id=task_id,
                    level="WARNING",
                    category="TASK_EXECUTE",
                    message=f"持仓不足，调整卖出股数：{target_shares} -> {actual_shares}"
                )
            else:
                actual_shares = target_shares

            if actual_shares <= 0:
                # 无股可卖，直接完成任务
                self.task_queue.log_task_message(
                    task_group_id=task['task_group_id'],
                    task_id=task_id,
                    level="INFO",
                    category="TASK_EXECUTE",
                    message="无持仓可卖出，任务直接完成"
                )
                self.update_task_status(task_id, TaskStatus.COMPLETED.value)
                return

            # 1. 记录交易指令到 trade_orders 表
            order_reason = task.get('task_params', {}).get('reason', 'ASYNC_SELL')
            trade_order_id = record_trade_order(stock_code, 'SELL', actual_shares, order_reason, order_uuid, ContextInfo)

            self.task_queue.log_task_message(
                task_group_id=task['task_group_id'],
                task_id=task_id,
                level="INFO",
                category="TASK_EXECUTE",
                message=f"已记录卖出订单到trade_orders表：ID={trade_order_id}"
            )

            # 2. 执行卖出订单，传入order_uuid
            order_id = self.place_sell_order(stock_code, actual_shares, order_uuid, ContextInfo)

            if order_id:
                # 更新任务状态为等待回调
                cursor = g_db_connection.cursor()
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = ?, order_id = ?
                    WHERE id = ?
                """, (TaskStatus.WAITING_CALLBACK.value, str(order_id), task_id))
                g_db_connection.commit()

                self.task_queue.log_task_message(
                    task_group_id=task['task_group_id'],
                    task_id=task_id,
                    level="INFO",
                    category="TASK_EXECUTE",
                    message=f"卖出订单已提交：{stock_code} {actual_shares}股，订单号：{order_id}"
                )
            else:
                raise Exception("下单失败，未获得订单号")

        except Exception as e:
            error_msg = f"执行卖出任务失败：{str(e)}"
            self.task_queue.log_task_message(
                task_group_id=task['task_group_id'],
                task_id=task_id,
                level="ERROR",
                category="TASK_EXECUTE",
                message=error_msg
            )
            self.update_task_status(task_id, TaskStatus.FAILED.value, error_msg)

    def execute_buy_cash_task(self, task: Dict, ContextInfo):
        """
        执行现金买入任务

        Args:
            task: 任务信息
            ContextInfo: iQuant上下文信息对象
        """
        task_id = task['id']
        stock_code = task['stock_code']
        target_shares = task['target_shares']
        order_uuid = task['order_uuid']

        try:
            # 获取当前可用现金
            account_info = get_account_info(ContextInfo)
            available_cash = account_info['available_cash']

            # 获取当前价格
            current_price = get_current_price(stock_code, ContextInfo)

            # 计算可买入股数（考虑手续费）
            estimated_fees_rate = COMMISSION_FEE_RATE
            max_amount = available_cash / (1 + estimated_fees_rate)
            max_shares = int(max_amount / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES

            # 确定实际买入股数
            task_params = task.get('task_params', {})
            use_all_cash = task_params.get('use_all_cash', False)
            buy_strategy = task_params.get('buy_strategy', '')

            if use_all_cash and buy_strategy == 'USE_ALL_CASH':
                # 510720转换场景：用尽现金买入模式，直接使用最大可买股数
                actual_shares = max_shares
            else:
                # 普通模式（包括159915买入）：取目标股数和最大可买股数的较小值
                actual_shares = min(target_shares, max_shares)

            # 确定买入模式描述
            if use_all_cash and buy_strategy == 'USE_ALL_CASH':
                mode_desc = "510720转换-用尽现金"
            else:
                mode_desc = "普通模式"

            self.task_queue.log_task_message(
                task_group_id=task['task_group_id'],
                task_id=task_id,
                level="INFO",
                category="TASK_EXECUTE",
                message=f"现金买入分析：可用现金{available_cash:.2f}，最大可买{max_shares}股，目标{target_shares}股，{mode_desc}，实际买入{actual_shares}股",
                extra_data={
                    'available_cash': available_cash,
                    'max_shares': max_shares,
                    'target_shares': target_shares,
                    'actual_shares': actual_shares,
                    'use_all_cash': use_all_cash,
                    'buy_strategy': buy_strategy,
                    'mode_desc': mode_desc,
                    'current_price': current_price
                }
            )

            if actual_shares >= MIN_TRADE_SHARES:
                # 1. 记录交易指令到 trade_orders 表
                order_reason = task.get('task_params', {}).get('reason', 'ASYNC_BUY_CASH')
                trade_order_id = record_trade_order(stock_code, 'BUY', actual_shares, order_reason, order_uuid, ContextInfo)

                self.task_queue.log_task_message(
                    task_group_id=task['task_group_id'],
                    task_id=task_id,
                    level="INFO",
                    category="TASK_EXECUTE",
                    message=f"已记录现金买入订单到trade_orders表：ID={trade_order_id}"
                )

                # 2. 执行买入订单，传入order_uuid
                order_id = self.place_buy_order(stock_code, actual_shares, order_uuid, ContextInfo)

                if order_id:
                    # 更新任务状态为等待回调
                    cursor = g_db_connection.cursor()
                    cursor.execute("""
                        UPDATE trade_task_queue
                        SET task_status = ?, order_id = ?, target_shares = ?
                        WHERE id = ?
                    """, (TaskStatus.WAITING_CALLBACK.value, str(order_id), actual_shares, task_id))
                    g_db_connection.commit()

                    self.task_queue.log_task_message(
                        task_group_id=task['task_group_id'],
                        task_id=task_id,
                        level="INFO",
                        category="TASK_EXECUTE",
                        message=f"现金买入订单已提交：{stock_code} {actual_shares}股，订单号：{order_id}"
                    )
                else:
                    raise Exception("下单失败，未获得订单号")
            else:
                # 现金不足，直接完成任务（100股都不够钱的情况）
                self.task_queue.log_task_message(
                    task_group_id=task['task_group_id'],
                    task_id=task_id,
                    level="WARNING",
                    category="TASK_EXECUTE",
                    message=f"现金不足买入最小单位，任务完成：可买{actual_shares}股 < 最小{MIN_TRADE_SHARES}股"
                )

                # 更新 target_shares 为实际买入股数（0股）
                cursor = g_db_connection.cursor()
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET target_shares = ?
                    WHERE id = ?
                """, (0, task_id))
                g_db_connection.commit()

                # 记录实际买入股数为0（用于融资买入任务计算剩余份额）
                g_trade_task_callback_handler.record_execution_result(
                    task_id=task_id,
                    actual_shares=0,
                    actual_price=current_price,
                    actual_amount=0.0
                )

                self.update_task_status(task_id, TaskStatus.COMPLETED.value)

        except Exception as e:
            error_msg = f"执行现金买入任务失败：{str(e)}"
            self.task_queue.log_task_message(
                task_group_id=task['task_group_id'],
                task_id=task_id,
                level="ERROR",
                category="TASK_EXECUTE",
                message=error_msg
            )
            self.update_task_status(task_id, TaskStatus.FAILED.value, error_msg)

    def execute_buy_margin_task(self, task: Dict, ContextInfo):
        """
        执行融资买入任务

        Args:
            task: 任务信息
            ContextInfo: iQuant上下文信息对象
        """
        task_id = task['id']
        stock_code = task['stock_code']
        task_group_id = task['task_group_id']
        order_uuid = task['order_uuid']

        try:
            # 从任务参数获取原始目标份额
            task_params = task.get('task_params', {})
            original_target = task_params.get('original_target_shares', 0)

            # 查询现金买入任务的实际执行结果
            cursor = g_db_connection.cursor()

            # 首先查询现金买入任务是否完成
            cursor.execute("""
                SELECT id FROM trade_task_queue
                WHERE task_group_id = ? AND task_type = ? AND task_status = 'COMPLETED'
            """, (task_group_id, TaskType.BUY_159915_CASH.value))

            cash_task_result = cursor.fetchone()
            cash_bought_shares = 0

            if cash_task_result:
                cash_task_id = cash_task_result[0]
                # 查询实际买入股数（从日志表中获取）
                cursor.execute("""
                    SELECT actual_shares FROM trade_task_log
                    WHERE task_id = ? AND execution_step = 'EXECUTION_RESULT' AND actual_shares IS NOT NULL
                    ORDER BY log_time DESC LIMIT 1
                """, (cash_task_id,))

                actual_shares_result = cursor.fetchone()
                if actual_shares_result:
                    cash_bought_shares = actual_shares_result[0]

            # 计算还需要买入的股数
            remaining_shares = original_target - cash_bought_shares

            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="INFO",
                category="TASK_EXECUTE",
                message=f"融资买入分析：原目标{original_target}股，现金已买{cash_bought_shares}股，剩余需买{remaining_shares}股"
            )

            if remaining_shares <= 0:
                # 现金已买够，无需融资
                self.task_queue.log_task_message(
                    task_group_id=task_group_id,
                    task_id=task_id,
                    level="INFO",
                    category="TASK_EXECUTE",
                    message="现金已买入足够股数，无需融资买入"
                )
                self.update_task_status(task_id, TaskStatus.COMPLETED.value)
                return

            # 检查融资额度
            account_info = get_account_info(ContextInfo)
            margin_available = account_info.get('credit_available', 0)

            if margin_available <= 0:
                self.task_queue.log_task_message(
                    task_group_id=task_group_id,
                    task_id=task_id,
                    level="WARNING",
                    category="TASK_EXECUTE",
                    message="无可用融资额度，任务完成"
                )
                self.update_task_status(task_id, TaskStatus.COMPLETED.value)
                return

            # 计算融资可买股数
            current_price = get_current_price(stock_code, ContextInfo)
            estimated_fees_rate = COMMISSION_FEE_RATE
            max_margin_amount = margin_available / (1 + estimated_fees_rate)
            max_margin_shares = int(max_margin_amount / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES

            # 确定实际融资买入股数
            actual_shares = min(remaining_shares, max_margin_shares)

            if actual_shares >= MIN_TRADE_SHARES:
                # 1. 记录交易指令到 trade_orders 表
                order_reason = task.get('task_params', {}).get('reason', 'ASYNC_BUY_MARGIN')
                trade_order_id = record_trade_order(stock_code, 'BUY', actual_shares, order_reason, order_uuid, ContextInfo)

                self.task_queue.log_task_message(
                    task_group_id=task_group_id,
                    task_id=task_id,
                    level="INFO",
                    category="TASK_EXECUTE",
                    message=f"已记录融资买入订单到trade_orders表：ID={trade_order_id}"
                )

                # 2. 执行融资买入订单，传入order_uuid
                order_id = self.place_margin_buy_order(stock_code, actual_shares, order_uuid, ContextInfo)

                if order_id:
                    # 更新任务状态为等待回调
                    cursor.execute("""
                        UPDATE trade_task_queue
                        SET task_status = ?, order_id = ?, target_shares = ?
                        WHERE id = ?
                    """, (TaskStatus.WAITING_CALLBACK.value, str(order_id), actual_shares, task_id))
                    g_db_connection.commit()

                    self.task_queue.log_task_message(
                        task_group_id=task_group_id,
                        task_id=task_id,
                        level="INFO",
                        category="TASK_EXECUTE",
                        message=f"融资买入订单已提交：{stock_code} {actual_shares}股，订单号：{order_id}"
                    )
                else:
                    raise Exception("融资下单失败，未获得订单号")
            else:
                # 融资额度不足（连100股都不够额度的情况）
                self.task_queue.log_task_message(
                    task_group_id=task_group_id,
                    task_id=task_id,
                    level="WARNING",
                    category="TASK_EXECUTE",
                    message=f"融资额度不足买入最小单位：可买{actual_shares}股 < 最小{MIN_TRADE_SHARES}股"
                )
                self.update_task_status(task_id, TaskStatus.COMPLETED.value)

        except Exception as e:
            error_msg = f"执行融资买入任务失败：{str(e)}"
            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="ERROR",
                category="TASK_EXECUTE",
                message=error_msg
            )
            self.update_task_status(task_id, TaskStatus.FAILED.value, error_msg)



    def place_sell_order(self, stock_code: str, shares: int, order_uuid: str, ContextInfo) -> Optional[str]:
        """
        下卖出订单

        Args:
            stock_code: 股票代码
            shares: 股数
            order_uuid: 订单UUID（用于回调匹配）
            ContextInfo: iQuant上下文信息对象

        Returns:
            Optional[str]: 临时订单ID，真实订单ID会在回调中获取
        """
        try:
            # 使用iQuant的passorder函数下单，第10个参数使用UUID作为备注
            result = passorder(
                34 if ACCOUNT_TYPE == "CREDIT" else 24,  # 卖出
                1101,
                ACCOUNT_ID,
                stock_code,
                44,  # 市价
                -1,  # 市价
                shares,
                '价值平均策略',
                1,
                order_uuid,  # 使用UUID作为备注，用于回调匹配
                ContextInfo
            )

            # 不再使用get_last_order_id，而是通过回调中的UUID匹配
            # 这里返回一个临时ID，真正的订单ID会在回调中获取
            return f"TEMP_SELL_{order_uuid[:8]}"

        except Exception as e:
            print(f"下卖出订单失败：{str(e)}")
            return None

    def place_buy_order(self, stock_code: str, shares: int, order_uuid: str, ContextInfo) -> Optional[str]:
        """
        下买入订单（现金）

        Args:
            stock_code: 股票代码
            shares: 股数
            order_uuid: 订单UUID（用于回调匹配）
            ContextInfo: iQuant上下文信息对象

        Returns:
            Optional[str]: 临时订单ID，真实订单ID会在回调中获取
        """
        try:
            # 使用iQuant的passorder函数下单，第10个参数使用UUID作为备注
            result = passorder(
                33 if ACCOUNT_TYPE == "CREDIT" else 23,  # 买入
                1101,
                ACCOUNT_ID,
                stock_code,
                44,  # 市价
                -1,  # 市价
                shares,
                '价值平均策略',
                1,
                order_uuid,  # 使用UUID作为备注，用于回调匹配
                ContextInfo
            )

            # 不再使用get_last_order_id，而是通过回调中的UUID匹配
            # 这里返回一个临时ID，真正的订单ID会在回调中获取
            return f"TEMP_BUY_{order_uuid[:8]}"

        except Exception as e:
            print(f"下买入订单失败：{str(e)}")
            return None

    def place_margin_buy_order(self, stock_code: str, shares: int, order_uuid: str, ContextInfo) -> Optional[str]:
        """
        下融资买入订单

        Args:
            stock_code: 股票代码
            shares: 股数
            order_uuid: 订单UUID（用于回调匹配）
            ContextInfo: iQuant上下文信息对象

        Returns:
            Optional[str]: 临时订单ID，真实订单ID会在回调中获取
        """
        try:
            # 融资买入，需要使用特定的订单类型
            # 这里可能需要根据iQuant的具体API调整参数
            result = passorder(
                27,  # 融资买入
                1101,  # 可能需要调整为融资买入的订单类型
                ACCOUNT_ID,
                stock_code,
                44,  # 市价
                -1,  # 市价
                shares,
                '价值平均策略',
                1,
                order_uuid,  # 使用UUID作为备注，用于回调匹配
                ContextInfo
            )

            # 不再使用get_last_order_id，而是通过回调中的UUID匹配
            # 这里返回一个临时ID，真正的订单ID会在回调中获取
            return f"TEMP_MARGIN_BUY_{order_uuid[:8]}"

        except Exception as e:
            print(f"下融资买入订单失败：{str(e)}")
            return None

    def check_timeout_tasks(self):
        """检查超时任务"""
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now()

            # 查找等待回调的任务
            cursor.execute("""
                SELECT id, task_group_id, started_time, order_id, stock_code, target_shares,
                       warning_logged, status_queried, alert_sent
                FROM trade_task_queue
                WHERE task_status = 'WAITING_CALLBACK'
                AND started_time IS NOT NULL
            """)

            waiting_tasks = cursor.fetchall()

            for task_row in waiting_tasks:
                task_id, task_group_id, started_time_str, order_id, stock_code, target_shares, warning_logged, status_queried, alert_sent = task_row

                # 计算等待时间
                started_time = datetime.datetime.strptime(started_time_str, "%Y-%m-%d %H:%M:%S")
                elapsed_seconds = (current_time - started_time).total_seconds()

                # 分级处理超时
                if elapsed_seconds > self.task_queue.timeout_levels['ALERT'] and not alert_sent:
                    self.handle_critical_timeout(task_id, task_group_id, order_id, elapsed_seconds)
                elif elapsed_seconds > self.task_queue.timeout_levels['QUERY'] and not status_queried:
                    self.handle_query_timeout(task_id, task_group_id, order_id, elapsed_seconds)
                elif elapsed_seconds > self.task_queue.timeout_levels['WARNING'] and not warning_logged:
                    self.handle_warning_timeout(task_id, task_group_id, elapsed_seconds)

        except Exception as e:
            error_msg = f"检查超时任务失败：{str(e)}"
            print(error_msg)
            log_message("ERROR", "超时检查", error_msg, None)

    def handle_warning_timeout(self, task_id: int, task_group_id: str, elapsed_seconds: float):
        """处理警告级超时"""
        try:
            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="WARNING",
                category="TIMEOUT",
                message=f"任务执行超时警告：已等待{elapsed_seconds:.0f}秒"
            )

            # 标记已记录警告
            cursor = g_db_connection.cursor()
            cursor.execute("""
                UPDATE trade_task_queue SET warning_logged = 1 WHERE id = ?
            """, (task_id,))
            g_db_connection.commit()

        except Exception as e:
            print(f"处理警告超时失败：{str(e)}")

    def handle_query_timeout(self, task_id: int, task_group_id: str, order_id: str, elapsed_seconds: float):
        """处理查询级超时"""
        try:
            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="INFO",
                category="TIMEOUT",
                message=f"主动查询订单状态：已等待{elapsed_seconds:.0f}秒，订单号：{order_id}"
            )

            # 查询订单状态
            self.query_order_status(task_id, task_group_id, order_id)

            # 标记已查询状态
            cursor = g_db_connection.cursor()
            cursor.execute("""
                UPDATE trade_task_queue SET status_queried = 1 WHERE id = ?
            """, (task_id,))
            g_db_connection.commit()

        except Exception as e:
            print(f"处理查询超时失败：{str(e)}")

    def handle_critical_timeout(self, task_id: int, task_group_id: str, order_id: str, elapsed_seconds: float):
        """处理严重超时"""
        try:
            alert_msg = f"交易任务严重超时！任务ID：{task_id}，订单ID：{order_id}，已超时{elapsed_seconds:.0f}秒"

            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="CRITICAL",
                category="TIMEOUT",
                message=alert_msg
            )

            # 发送告警（可以扩展为邮件、微信等）
            print(f"🚨 严重告警：{alert_msg}")

            # 标记已发送告警
            cursor = g_db_connection.cursor()
            cursor.execute("""
                UPDATE trade_task_queue SET alert_sent = 1 WHERE id = ?
            """, (task_id,))
            g_db_connection.commit()

        except Exception as e:
            print(f"处理严重超时失败：{str(e)}")

    def query_order_status(self, task_id: int, task_group_id: str, order_id: str):
        """查询订单状态"""
        try:
            # 使用iQuant API查询订单状态
            order_obj = get_value_by_order_id(order_id, ACCOUNT_ID, ACCOUNT_TYPE, 'order')

            if order_obj:
                status_code = order_obj.m_nOrderStatus
                status_desc = ORDER_STATUS_MAP.get(status_code, f'未知状态({status_code})')

                self.task_queue.log_task_message(
                    task_group_id=task_group_id,
                    task_id=task_id,
                    level="INFO",
                    category="STATUS_QUERY",
                    message=f"订单状态查询结果：{status_desc}",
                    extra_data={
                        'order_id': order_id,
                        'status_code': status_code,
                        'status_desc': status_desc
                    }
                )

                # 如果订单已完成，手动触发回调处理
                if status_code in [56, 54, 57]:  # 已成、已撤、废单
                    self.handle_manual_callback(task_id, order_obj)

            else:
                self.task_queue.log_task_message(
                    task_group_id=task_group_id,
                    task_id=task_id,
                    level="WARNING",
                    category="STATUS_QUERY",
                    message=f"无法查询到订单信息：{order_id}"
                )

        except Exception as e:
            error_msg = f"查询订单状态失败：{str(e)}"
            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="ERROR",
                category="STATUS_QUERY",
                message=error_msg
            )

    def handle_manual_callback(self, task_id: int, order_obj):
        """手动处理回调（当主动查询发现订单已完成时）"""
        try:
            # 模拟回调处理
            callback_handler = TradeTaskCallbackHandler()
            callback_handler.handle_manual_order_callback_by_task_id(task_id, order_obj)

        except Exception as e:
            print(f"手动处理回调失败：{str(e)}")

class TradeTaskCallbackHandler:
    """交易任务回调处理器"""

    def __init__(self):
        self.task_queue = g_trade_task_queue

    def handle_order_callback(self, orderInfo):
        """
        处理订单回调

        Args:
            orderInfo: iQuant订单回调信息
        """
        try:
            # 调试：打印所有属性
            print("🔍 订单对象所有属性：")
            for attr in dir(orderInfo):
                if not attr.startswith('_'):
                    try:
                        value = getattr(orderInfo, attr)
                        if not callable(value):
                            print(f"   {attr} = {value}")
                    except:
                        pass

            # 获取订单UUID（从备注字段）
            order_uuid = str(getattr(orderInfo, 'm_strRemark', ''))
            if not order_uuid:
                print("❌ 无法获取订单UUID（m_strRemark为空）")
                return

            # 尝试多种可能的订单ID属性名
            order_id = None
            possible_id_attrs = ['m_strOrderSysID', 'm_strOrderID', 'm_strOrderRef', 'm_nOrderRef']
            for attr in possible_id_attrs:
                try:
                    temp_id = str(getattr(orderInfo, attr, ''))
                    if temp_id and temp_id != '' and temp_id != '0':
                        order_id = temp_id
                        print(f"✓ 找到订单ID属性：{attr} = {order_id}")
                        break
                except:
                    continue

            if not order_id:
                print("❌ 无法获取有效的订单ID")
                return

            order_status = getattr(orderInfo, 'm_nOrderStatus', 0)
            instrument_id = str(getattr(orderInfo, 'm_strInstrumentID', ''))
            volume_traded = getattr(orderInfo, 'm_nVolumeTraded', 0)
            volume_total = getattr(orderInfo, 'm_nVolumeTotalOriginal', 0)

            # 订单状态映射
            status_map = {
                0: "等待结束", 48: "未报", 49: "待报", 50: "已报",
                51: "已报待撤", 52: "部成待撤", 53: "部撤", 54: "已撤",
                55: "部成", 56: "已成", 57: "废单", 86: "已确认", 255: "未知"
            }

            status_desc = status_map.get(order_status, f"未知状态({order_status})")

            print(f"收到订单回调：UUID={order_uuid}，订单ID={order_id}，状态={order_status}({status_desc})，股票={instrument_id}，成交量={volume_traded}/{volume_total}")
            log_message("INFO", "订单回调", f"UUID={order_uuid}，订单ID={order_id}，状态={order_status}({status_desc})，股票={instrument_id}，成交量={volume_traded}/{volume_total}")

            # 根据UUID查找对应的任务
            task = self.find_task_by_order_uuid(order_uuid, TaskStatus.WAITING_CALLBACK.value)

            if task:
                self.process_order_callback_with_uuid(task, orderInfo, order_uuid, order_id, status_desc)
            else:
                print(f"⚠️ 未找到匹配的任务：UUID={order_uuid}")

        except Exception as e:
            error_msg = f"处理订单回调失败：{str(e)}"
            print(error_msg)
            log_message("ERROR", "回调处理", error_msg, None)

    def handle_deal_callback(self, dealInfo):
        """
        处理成交回调

        Args:
            dealInfo: iQuant成交回调信息
        """
        try:
            # 调试：打印所有属性
            print("🔍 成交对象所有属性：")
            for attr in dir(dealInfo):
                if not attr.startswith('_'):
                    try:
                        value = getattr(dealInfo, attr)
                        if not callable(value):
                            print(f"   {attr} = {value}")
                    except:
                        pass

            # 获取订单UUID（从备注字段）
            order_uuid = str(getattr(dealInfo, 'm_strRemark', ''))
            if not order_uuid:
                print("❌ 无法获取订单UUID（m_strRemark为空）")
                return

            # 尝试多种可能的订单ID属性名
            order_id = None
            possible_id_attrs = ['m_strOrderSysID', 'm_strOrderID', 'm_strOrderRef', 'm_nOrderRef']
            for attr in possible_id_attrs:
                try:
                    temp_id = str(getattr(dealInfo, attr, ''))
                    if temp_id and temp_id != '' and temp_id != '0':
                        order_id = temp_id
                        print(f"✓ 找到订单ID属性：{attr} = {order_id}")
                        break
                except:
                    continue

            if not order_id:
                print("❌ 无法获取有效的订单ID")
                return

            # 根据UUID查找对应的任务(这里要找到已成交的记录)
            task = self.find_task_by_order_uuid(order_uuid, TaskStatus.COMPLETED.value)

            if task:
                self.process_deal_callback_with_uuid(task, dealInfo, order_uuid, order_id)
            else:
                print(f"⚠️ 未找到匹配的任务：UUID={order_uuid}")

        except Exception as e:
            error_msg = f"处理成交回调失败：{str(e)}"
            print(error_msg)
            log_message("ERROR", "回调处理", error_msg, None)

    def find_task_by_order_id(self, order_id: str) -> Optional[Dict]:
        """
        根据订单ID查找任务

        Args:
            order_id: 订单ID

        Returns:
            Optional[Dict]: 任务信息，未找到返回None
        """
        try:
            cursor = g_db_connection.cursor()
            cursor.execute("""
                SELECT * FROM trade_task_queue
                WHERE order_id = ? AND task_status = 'WAITING_CALLBACK'
            """, (order_id,))

            result = cursor.fetchone()
            if result:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, result))

            return None

        except Exception as e:
            print(f"查找任务失败：{str(e)}")
            return None

    def find_task_by_order_uuid(self, order_uuid: str, task_status: str) -> Optional[Dict]:
        """根据订单UUID查找任务"""
        try:
            cursor = g_db_connection.cursor()
            cursor.execute("""
                SELECT * FROM trade_task_queue
                WHERE order_uuid = ? AND task_status = ?
            """, (order_uuid, task_status))

            result = cursor.fetchone()
            if result:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, result))
            return None

        except Exception as e:
            print(f"根据UUID查找任务失败：{str(e)}")
            return None

    def process_order_callback_with_uuid(self, task: Dict, orderInfo, order_uuid: str, order_id: str, status_desc: str):
        """处理带UUID的订单回调"""
        try:
            task_id = task['id']
            order_status = getattr(orderInfo, 'm_nOrderStatus', 0)
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 记录订单状态历史
            cursor = g_db_connection.cursor()
            instrument_id = str(getattr(orderInfo, 'm_strInstrumentID', ''))
            volume_traded = getattr(orderInfo, 'm_nVolumeTraded', 0)
            volume_total = getattr(orderInfo, 'm_nVolumeTotalOriginal', 0)

            # 插入状态历史记录
            cursor.execute("""
                INSERT INTO order_status_history
                (order_uuid, order_id, stock_code, order_status, status_desc,
                 volume_traded, volume_total, callback_time, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (order_uuid, order_id, instrument_id, order_status, status_desc,
                  volume_traded, volume_total, current_time, current_time))

            # 更新任务中的真实订单ID
            cursor.execute("""
                UPDATE trade_task_queue
                SET order_id = ?
                WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
            """, (order_id, order_uuid))

            print(f"✓ 订单状态历史已记录：UUID={order_uuid}，状态={status_desc}")

            # 处理订单最终状态
            final_statuses = {
                54: ("CANCELLED", "已撤销"),    # 已撤
                56: ("COMPLETED", "已成交"),    # 已成
                57: ("FAILED", "废单")          # 废单
            }

            if order_status in final_statuses:
                task_status, final_desc = final_statuses[order_status]

                # 更新任务状态
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = ?, completed_time = ?
                    WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
                """, (task_status, current_time, order_uuid))

                # 同时更新 trade_orders 表状态
                trade_order_status = "SUCCESS" if task_status == "COMPLETED" else "FAILED"
                cursor.execute("""
                    UPDATE trade_orders
                    SET order_status = ?, execution_time = ?
                    WHERE order_uuid = ? AND order_status = 'PENDING'
                """, (trade_order_status, current_time, order_uuid))

                trade_orders_updated = cursor.rowcount
                print(f"✓ trade_orders 状态已更新：{trade_orders_updated} 条记录，状态={trade_order_status}")

                print(f"✓ 任务状态已更新：UUID={order_uuid}，任务ID={task_id}，状态={task_status}({final_desc})")
                log_message("INFO", "任务状态更新", f"UUID={order_uuid}的任务状态更新为{task_status}({final_desc})")

            # 处理部分成交状态
            elif order_status == 55:  # 部成
                print(f"📊 订单部分成交：UUID={order_uuid}，已成交{volume_traded}/{volume_total}股")
                log_message("INFO", "部分成交", f"UUID={order_uuid}，已成交{volume_traded}/{volume_total}股")

            # 处理其他中间状态
            elif order_status in [48, 49, 50, 51, 52, 53]:
                print(f"📋 订单状态更新：UUID={order_uuid}，状态={status_desc}")
                log_message("INFO", "状态更新", f"UUID={order_uuid}，状态={status_desc}")

            g_db_connection.commit()

        except Exception as e:
            print(f"处理UUID订单回调失败：{str(e)}")

    def query_and_aggregate_deal_records(self, order_uuid: str) -> Dict:
        """
        查询所有相关的成交记录并汇总

        Args:
            order_uuid: 订单UUID

        Returns:
            dict: 汇总的成交数据
        """
        try:
            print(f"🔍 查询订单UUID={order_uuid}的所有成交记录...")

            # 查询所有成交记录
            all_deals = get_trade_detail_data(ACCOUNT_ID, ACCOUNT_TYPE, 'DEAL')

            # 筛选出与当前订单相关的成交记录
            related_deals = []
            for deal in all_deals:
                deal_uuid = str(getattr(deal, 'm_strRemark', ''))
                if deal_uuid == order_uuid:
                    related_deals.append(deal)

            print(f"📊 找到{len(related_deals)}条相关成交记录")

            if not related_deals:
                return {'total_shares': 0, 'avg_price': 0.0, 'total_amount': 0.0, 'total_commission': 0.0}

            # 汇总计算
            total_shares = 0
            total_amount = 0.0
            total_commission = 0.0

            for i, deal in enumerate(related_deals):
                # 获取成交量
                shares = 0
                for attr in ['m_nVolume', 'm_nDealVol', 'm_nTradeVolume']:
                    try:
                        temp_vol = getattr(deal, attr, 0)
                        if temp_vol > 0:
                            shares = temp_vol
                            break
                    except:
                        continue

                # 获取成交价
                price = 0.0
                for attr in ['m_dPrice', 'm_dDealPrice', 'm_dTradePrice', 'm_dAvgPrice']:
                    try:
                        temp_price = getattr(deal, attr, 0.0)
                        if temp_price > 0:
                            price = temp_price
                            break
                    except:
                        continue

                # 获取成交金额
                amount = 0.0
                for attr in ['m_dTradeAmount', 'm_dDealAmount', 'm_dAmount']:
                    try:
                        temp_amount = getattr(deal, attr, 0.0)
                        if temp_amount > 0:
                            amount = temp_amount
                            break
                    except:
                        continue

                # 如果没有找到成交金额，用成交量*成交价计算
                if amount == 0.0 and shares > 0 and price > 0:
                    amount = shares * price

                # 获取手续费
                commission = 0.0
                for attr in ['m_dCommission', 'm_dComssion', 'm_dFee', 'm_dTradeFee']:
                    try:
                        temp_commission = getattr(deal, attr, 0.0)
                        if temp_commission > 0:
                            commission = temp_commission
                            break
                    except:
                        continue

                print(f"  成交记录{i+1}: {shares}股 × {price:.4f}元 = {amount:.2f}元, 手续费{commission:.2f}元")

                # 累加到总计
                total_shares += shares
                total_amount += amount
                total_commission += commission

            # 计算成交均价
            avg_price = total_amount / total_shares if total_shares > 0 else 0.0

            print(f"📈 汇总结果: 总股数={total_shares}, 总金额={total_amount:.2f}, 成交均价={avg_price:.4f}, 总手续费={total_commission:.2f}")

            return {
                'total_shares': total_shares,
                'avg_price': avg_price,
                'total_amount': total_amount,
                'total_commission': total_commission
            }

        except Exception as e:
            print(f"❌ 查询和汇总成交记录失败：{str(e)}")
            import traceback
            traceback.print_exc()
            return {'total_shares': 0, 'avg_price': 0.0, 'total_amount': 0.0, 'total_commission': 0.0}

    def process_deal_callback_with_uuid(self, task: Dict, dealInfo, order_uuid: str, order_id: str):
        """处理带UUID的成交回调 - 增强版：查询所有相关成交记录并汇总"""
        try:
            print(f"🔄 开始处理成交回调：UUID={order_uuid}, 订单ID={order_id}")

            # 查询所有相关的成交记录并汇总
            aggregated_data = self.query_and_aggregate_deal_records(order_uuid)

            if aggregated_data and aggregated_data['total_shares'] > 0:
                task_id = task['id']
                total_shares = aggregated_data['total_shares']
                avg_price = aggregated_data['avg_price']
                total_amount = aggregated_data['total_amount']
                total_commission = aggregated_data['total_commission']

                print(f"📊 汇总成交数据：总股数={total_shares}, 均价={avg_price:.4f}, 总金额={total_amount:.2f}, 总手续费={total_commission:.2f}")

                # 获取任务信息判断订单状态和计算费用
                cursor = g_db_connection.cursor()
                cursor.execute("""
                    SELECT task_type, stock_code, target_shares
                    FROM trade_task_queue
                    WHERE id = ?
                """, (task_id,))
                task_info = cursor.fetchone()

                if task_info:
                    task_type, stock_code, target_shares = task_info

                    # 判断订单状态：总成交股数 != 目标股数 = 部分成交
                    order_status = 'PARTIAL' if total_shares != target_shares else 'SUCCESS'

                    print(f"📋 订单状态判断：目标股数={target_shares}, 实际成交={total_shares}, 状态={order_status}")

                    # 计算完整的费用信息
                    trade_type_str = 'SELL' if 'SELL' in task_type else 'BUY'

                    # 计算印花税
                    stamp_tax = total_amount * SELL_TAX_RATE if trade_type_str == 'SELL' else 0.0

                    # 计算过户费
                    calculated_fees = calculate_trading_fees(total_amount, total_shares, trade_type_str, stock_code)
                    transfer_fee = calculated_fees['transfer_fee']

                    # 计算总费用
                    total_fees = total_commission + stamp_tax + transfer_fee
                    net_amount = total_amount - total_fees

                    print(f"💰 费用计算：佣金{total_commission:.2f} + 印花税{stamp_tax:.2f} + 过户费{transfer_fee:.2f} = 总费用{total_fees:.2f}, 净额{net_amount:.2f}")

                    # 记录执行结果到 trade_task_log 表（用于融资买入任务计算剩余份额）
                    self.record_execution_result(task_id, total_shares, avg_price, total_amount)

                    # 同步更新 trade_orders 表的成交信息
                    self.sync_trade_orders_status(task_id, order_status, None, total_shares, avg_price)

                    # 记录交易执行日志
                    self.record_trade_execution_enhanced(
                        order_uuid=order_uuid,
                        trade_type=trade_type_str,
                        stock_code=stock_code,
                        shares=total_shares,
                        price=avg_price,
                        amount=total_amount,
                        total_fees=total_fees,
                        order_id=order_id,
                        commission=total_commission,
                        stamp_tax=stamp_tax,
                        transfer_fee=transfer_fee,
                        net_amount=net_amount,
                        status=order_status
                    )

                    print(f"✅ 成交回调处理完成：任务ID={task_id}, 汇总成交{total_shares}股, 状态={order_status}")

                    # 交易完成后更新账户信息和持仓记录
                    self.update_account_and_position_after_trade(task_id)

                else:
                    print("❌ 未找到对应的任务信息")

            else:
                print("⚠️ 未找到有效的成交记录或成交数据为空")

        except Exception as e:
            error_msg = f"处理成交回调失败：{str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()

    def record_trade_execution_enhanced(self, order_uuid: str, trade_type: str, stock_code: str,
                                      shares: int, price: float, amount: float, total_fees: float,
                                      order_id: str, commission: float, stamp_tax: float,
                                      transfer_fee: float, net_amount: float, status: str):
        """记录增强的交易执行详情（支持汇总数据）"""
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 插入或更新交易执行记录
            cursor.execute("""
                INSERT OR REPLACE INTO trade_execution_log
                (trade_time, trade_type, stock_code, shares, price, amount, fees,
                 order_id, order_uuid, status, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (current_time, trade_type, stock_code, shares, price, amount, total_fees,
                  order_id, order_uuid, status, current_time))

            execution_log_id = cursor.lastrowid

            # 插入费用明细记录
            cursor.execute("""
                INSERT OR REPLACE INTO trade_fee_details
                (execution_log_id, order_uuid, commission, stamp_tax, transfer_fee,
                 other_fees, total_fees, net_amount, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (execution_log_id, order_uuid, commission, stamp_tax, transfer_fee,
                  0.0, total_fees, net_amount, current_time))

            # 更新 trade_orders 表的成交信息
            cursor.execute("""
                UPDATE trade_orders
                SET order_status = ?,
                    actual_shares = ?,
                    actual_price = ?,
                    execution_time = ?
                WHERE order_uuid = ?
            """, (status, shares, price, current_time, order_uuid))

            g_db_connection.commit()

            print(f"✅ 交易执行记录已保存：执行日志ID={execution_log_id}, 订单状态={status}")

        except Exception as e:
            print(f"❌ 记录交易执行详情失败：{str(e)}")
            import traceback
            traceback.print_exc()

    def record_trade_execution(self, order_uuid: str, trade_type: str, stock_code: str,
                             shares: int, price: float, amount: float, total_fees: float,
                             order_id: str, commission: float, stamp_tax: float,
                             transfer_fee: float, net_amount: float):
        """记录交易执行详情（保持向后兼容）"""
        self.record_trade_execution_enhanced(
            order_uuid, trade_type, stock_code, shares, price, amount, total_fees,
            order_id, commission, stamp_tax, transfer_fee, net_amount, 'SUCCESS'
        )

    def process_order_callback_manual(self, task: Dict, orderInfo):
        """
        处理订单状态回调

        Args:
            task: 任务信息
            orderInfo: 订单回调信息
        """
        task_id = task['id']
        task_group_id = task['task_group_id']
        order_status = orderInfo.m_nOrderStatus

        try:
            # 记录回调信息
            self.record_callback_data_manual(task_id, 'ORDER_CALLBACK', orderInfo)

            status_desc = ORDER_STATUS_MAP.get(order_status, f'未知状态({order_status})')

            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="INFO",
                category="CALLBACK",
                message=f"收到订单回调：{status_desc}",
                extra_data={
                    'order_status': order_status,
                    'status_desc': status_desc
                }
            )

            # 根据订单状态更新任务
            if order_status == 56:  # 已成
                self.complete_task(task_id, task_group_id)
            elif order_status in [54, 57]:  # 已撤、废单
                error_msg = f"订单失败：{status_desc}"
                g_trade_task_executor.update_task_status(task_id, TaskStatus.FAILED.value, error_msg)
                # 同步更新 trade_orders 表的状态
                self.sync_trade_orders_status(task_id, 'FAILED', error_msg)

                # 获取任务信息用于记录交易失败日志
                try:
                    cursor = g_db_connection.cursor()
                    cursor.execute("""
                        SELECT task_type, stock_code, target_shares
                        FROM trade_task_queue
                        WHERE id = ?
                    """, (task_id,))
                    task_info = cursor.fetchone()

                    if task_info:
                        task_type, stock_code, target_shares = task_info
                        order_type = "BUY" if "BUY" in task_type else "SELL"

                        # 记录到主日志系统
                        log_message("ERROR", "交易执行",
                                   f"[实盘交易] 交易失败：{order_type} {stock_code} {target_shares}股，原因：VALUE_AVERAGE，错误：{error_msg}",
                                   None, None)
                except Exception as e:
                    print(f"记录交易失败日志失败：{str(e)}")

        except Exception as e:
            error_msg = f"处理订单回调失败：{str(e)}"
            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="ERROR",
                category="CALLBACK",
                message=error_msg
            )


    def complete_task(self, task_id: int, task_group_id: str):
        """
        完成任务

        Args:
            task_id: 任务ID
            task_group_id: 任务组ID
        """
        try:
            # 更新任务状态为完成
            g_trade_task_executor.update_task_status(task_id, TaskStatus.COMPLETED.value)

            # 同步更新 trade_orders 表的状态
            self.sync_trade_orders_status(task_id, 'SUCCESS')

            # 获取任务信息用于记录交易成功日志
            try:
                cursor = g_db_connection.cursor()
                cursor.execute("""
                    SELECT task_type, stock_code, target_shares
                    FROM trade_task_queue
                    WHERE id = ?
                """, (task_id,))
                task_info = cursor.fetchone()

                if task_info:
                    task_type, stock_code, target_shares = task_info
                    order_type = "BUY" if "BUY" in task_type else "SELL"

                    # 记录到主日志系统
                    log_message("INFO", "交易执行",
                               f"[实盘交易] 交易成功：{order_type} {stock_code} {target_shares}股，原因：VALUE_AVERAGE",
                               None, None)
            except Exception as e:
                print(f"记录交易成功日志失败：{str(e)}")

            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="INFO",
                category="TASK_COMPLETE",
                message="任务执行完成"
            )

            # 检查是否需要创建账户快照
            self.create_completion_snapshot(task_id, task_group_id)

        except Exception as e:
            error_msg = f"完成任务失败：{str(e)}"
            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="ERROR",
                category="TASK_COMPLETE",
                message=error_msg
            )

    def record_callback_data_manual(self, task_id: int, callback_type: str, callback_obj):
        """
        记录回调数据到 trade_task_log 表

        Args:
            task_id: 任务ID
            callback_type: 回调类型
            callback_obj: 回调对象
        """
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 提取回调对象的属性
            callback_data = {}
            for attr in dir(callback_obj):
                if not attr.startswith('_'):
                    try:
                        value = getattr(callback_obj, attr)
                        if not callable(value):
                            callback_data[attr] = value
                    except:
                        pass

            # 获取任务组ID
            cursor = g_db_connection.cursor()
            cursor.execute("SELECT task_group_id FROM trade_task_queue WHERE id = ?", (task_id,))
            result = cursor.fetchone()
            task_group_id = result[0] if result else None

            cursor.execute("""
                INSERT INTO trade_task_log
                (task_id, task_group_id, log_level, log_category, log_message,
                 extra_data, log_time, execution_step, step_status, callback_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_id,
                task_group_id,
                'INFO',
                'CALLBACK',
                f'收到{callback_type}回调',
                None,
                current_time,
                callback_type,
                'RECEIVED',
                json.dumps(callback_data)
            ))

            g_db_connection.commit()

        except Exception as e:
            print(f"记录回调数据失败：{str(e)}")

    def record_execution_result(self, task_id: int, actual_shares: int, actual_price: float, actual_amount: float):
        """
        记录执行结果到 trade_task_log 表

        Args:
            task_id: 任务ID
            actual_shares: 实际股数
            actual_price: 实际价格
            actual_amount: 实际金额
        """
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 计算实际费用
            cursor = g_db_connection.cursor()
            cursor.execute("SELECT task_type, task_group_id FROM trade_task_queue WHERE id = ?", (task_id,))
            result = cursor.fetchone()
            if result:
                task_type, task_group_id = result
            else:
                task_type, task_group_id = '', None

            is_sell = task_type == TaskType.SELL_510720.value
            actual_fees = g_trade_task_queue.calculate_fees(actual_amount, is_sell)

            cursor.execute("""
                INSERT INTO trade_task_log
                (task_id, task_group_id, log_level, log_category, log_message,
                 log_time, execution_step, step_status, actual_shares, actual_price,
                 actual_amount, actual_fees)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_id,
                task_group_id,
                'INFO',
                'EXECUTION_RESULT',
                f'任务执行完成：{actual_shares}股 @ {actual_price}元',
                current_time,
                'EXECUTION_RESULT',
                'COMPLETED',
                actual_shares,
                actual_price,
                actual_amount,
                actual_fees
            ))

            g_db_connection.commit()

        except Exception as e:
            print(f"记录执行结果失败：{str(e)}")

    def create_completion_snapshot(self, task_id: int, task_group_id: str):
        """
        创建任务完成时的账户快照

        Args:
            task_id: 任务ID
            task_group_id: 任务组ID
        """
        try:
            # 获取任务类型
            cursor = g_db_connection.cursor()
            cursor.execute("SELECT task_type FROM trade_task_queue WHERE id = ?", (task_id,))
            result = cursor.fetchone()
            task_type = result[0] if result else ''

            # 根据任务类型确定快照时点
            if task_type == TaskType.SELL_510720.value:
                snapshot_point = SnapshotPoint.AFTER_SELL.value
            elif task_type == TaskType.BUY_159915_CASH.value:
                snapshot_point = SnapshotPoint.AFTER_BUY_CASH.value
            elif task_type == TaskType.BUY_159915_MARGIN.value:
                snapshot_point = SnapshotPoint.AFTER_BUY_MARGIN.value
            else:
                return  # 未知任务类型，不创建快照

            # 创建快照（注意：这里需要ContextInfo，但在回调中可能没有）
            # 可以考虑在主循环中延迟创建快照
            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="INFO",
                category="SNAPSHOT",
                message=f"需要创建账户快照：{snapshot_point}"
            )

        except Exception as e:
            print(f"创建完成快照失败：{str(e)}")

    def sync_trade_orders_status(self, task_id: int, order_status: str, error_message: str = None,
                                actual_shares: int = None, actual_price: float = None):
        """
        同步更新 trade_orders 表的订单状态

        Args:
            task_id: 任务ID
            order_status: 订单状态 ('SUCCESS', 'FAILED', 'CANCELLED')
            error_message: 错误信息（可选）
            actual_shares: 实际成交股数（可选）
            actual_price: 实际成交价格（可选）
        """
        try:
            cursor = g_db_connection.cursor()

            # 根据任务ID查找对应的 trade_orders 记录和任务信息
            cursor.execute("""
                SELECT tq.stock_code, tq.created_time, tq.task_type, tq.target_shares
                FROM trade_task_queue tq
                WHERE tq.id = ?
            """, (task_id,))

            task_result = cursor.fetchone()
            if not task_result:
                print(f"警告：未找到任务ID {task_id} 的信息")
                return

            stock_code, task_created_time, task_type, target_shares = task_result

            # 根据任务类型确定订单类型
            if 'SELL' in task_type:
                order_type = 'SELL'
            elif 'BUY' in task_type:
                order_type = 'BUY'
            else:
                print(f"警告：无法确定任务类型 {task_type} 对应的订单类型")
                return

            # 如果是失败状态，设置实际成交为0
            if order_status in ['FAILED', 'CANCELLED']:
                actual_shares = 0
                actual_price = 0.0
            elif order_status == 'SUCCESS' and actual_shares is None:
                # 如果是成功状态但没有提供实际成交数据，使用目标股数
                actual_shares = target_shares

            # 查找对应的 trade_orders 记录（按时间和股票代码匹配）
            cursor.execute("""
                UPDATE trade_orders
                SET order_status = ?, error_message = ?, execution_time = ?,
                    actual_shares = ?, actual_price = ?
                WHERE stock_code = ?
                AND order_type = ?
                AND order_status = 'PENDING'
                AND ABS(julianday(created_time) - julianday(?)) < 0.01
            """, (order_status, error_message,
                  datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                  actual_shares, actual_price,
                  stock_code, order_type, task_created_time))

            if cursor.rowcount > 0:
                g_db_connection.commit()
                print(f"✓ 已同步更新 trade_orders 表状态：任务ID={task_id}，状态={order_status}，成交股数={actual_shares}")
            else:
                print(f"警告：未找到匹配的 trade_orders 记录进行状态同步：任务ID={task_id}")

        except Exception as e:
            print(f"同步 trade_orders 状态失败：{str(e)}")

    def handle_manual_order_callback_by_task_id(self, task_id: int, order_obj):
        """
        根据任务ID处理订单回调（用于超时查询后的手动处理）

        Args:
            task_id: 任务ID
            order_obj: 订单对象
        """
        try:
            # 获取任务信息
            cursor = g_db_connection.cursor()
            cursor.execute("SELECT * FROM trade_task_queue WHERE id = ?", (task_id,))
            result = cursor.fetchone()

            if result:
                columns = [desc[0] for desc in cursor.description]
                task = dict(zip(columns, result))

                # 处理回调
                self.process_order_callback_manual(task, order_obj)
            else:
                print(f"未找到任务ID：{task_id}")

        except Exception as e:
            print(f"根据任务ID处理回调失败：{str(e)}")

    def record_trade_fee_details(self, order_uuid: str, fee_details: Dict):
        """记录交易费用明细"""
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 查找对应的 trade_execution_log 记录ID
            cursor.execute("""
                SELECT id FROM trade_execution_log
                WHERE order_uuid = ? AND status = 'SUCCESS'
                ORDER BY id DESC LIMIT 1
            """, (order_uuid,))

            execution_record = cursor.fetchone()
            execution_log_id = execution_record[0] if execution_record else None

            # 插入费用明细记录
            cursor.execute("""
                INSERT INTO trade_fee_details
                (execution_log_id, order_uuid, commission, stamp_tax, transfer_fee,
                 other_fees, total_fees, net_amount, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                execution_log_id,
                order_uuid,
                fee_details['commission'],
                fee_details['stamp_tax'],
                fee_details['transfer_fee'],
                0.0,  # other_fees
                fee_details['total_fees'],
                fee_details['net_amount'],
                current_time
            ))

            g_db_connection.commit()
        except Exception as e:
            import traceback
            traceback.print_exc()

    def record_position_after_trade(self, stock_code: str, shares: int, price: float, trade_type: str):
        """交易后记录持仓信息"""
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 获取当前真实持仓信息来计算平均成本
            cursor.execute("""
                SELECT shares, avg_cost FROM position_records
                WHERE stock_code = ?
                ORDER BY id DESC LIMIT 1
            """, (stock_code,))

            last_position = cursor.fetchone()

            if last_position:
                old_shares, old_avg_cost = last_position

                # 计算新的持仓和平均成本
                if trade_type == "BUY":
                    new_shares = old_shares + shares
                    new_avg_cost = ((old_shares * old_avg_cost) + (shares * price)) / new_shares if new_shares > 0 else price
                else:  # SELL
                    new_shares = old_shares - shares
                    new_avg_cost = old_avg_cost  # 卖出不改变平均成本
            else:
                if trade_type == "BUY":
                    new_shares = shares
                    new_avg_cost = price
                else:  # SELL - 不应该发生，但防御性处理
                    new_shares = -shares
                    new_avg_cost = price

            # 计算市值
            market_value = new_shares * price

            # 如果是159915，从策略状态获取真实的期数和目标价值
            period_number = None
            target_value = None
            if stock_code == ACTIVE_FUND_CODE and g_strategy_status:
                period_number = g_strategy_status.get('current_period', 0)
                if period_number > 0:
                    target_value = period_number * PERIOD_INVESTMENT_AMOUNT

            # 插入持仓记录
            cursor.execute("""
                INSERT INTO position_records
                (record_date, stock_code, shares, avg_cost, market_value, current_price,
                 period_number, target_value, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                current_time, stock_code, new_shares, new_avg_cost, market_value, price,
                period_number, target_value, current_time
            ))

            g_db_connection.commit()
        except Exception as e:
            import traceback
            traceback.print_exc()

    def update_account_info_after_trade(self, task_id: int):
        """交易完成后更新账户信息"""
        # TODO 这个可能只记录了一下日志trade_task_log，并没有真正更新账户信息
        try:
            # 注意：在成交回调中无法直接获取ContextInfo来查询实时账户信息
            # 这里记录一个标记，表示需要在主循环中更新账户信息

            # 记录到任务日志中，提醒主循环更新账户信息
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 获取任务组ID用于日志记录
            cursor.execute("SELECT task_group_id FROM trade_task_queue WHERE id = ?", (task_id,))
            result = cursor.fetchone()
            task_group_id = result[0] if result else "unknown"

            # 记录需要更新账户信息的标记
            cursor.execute("""
                INSERT INTO trade_task_log
                (task_id, task_group_id, log_level, log_category, log_message, log_time)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                task_id,
                task_group_id,
                "INFO",
                "ACCOUNT_UPDATE_NEEDED",
                "交易完成，需要在主循环中更新账户信息",
                current_time
            ))

            g_db_connection.commit()
        except Exception as e:
            import traceback
            traceback.print_exc()

    def update_account_and_position_after_trade(self, task_id: int):
        """交易完成后更新账户信息和持仓记录（保留原方法名兼容性）"""
        self.update_account_info_after_trade(task_id)


# 全局任务执行器和回调处理器实例
g_trade_task_executor = TradeTaskExecutor()
g_trade_task_callback_handler = TradeTaskCallbackHandler()


# ==================== 回调函数集成 ====================

def task_queue_order_callback(ContextInfo, orderInfo):
    """
    任务队列订单回调函数
    需要在iQuant的order_callback中调用此函数
    """
    try:
        g_trade_task_callback_handler.handle_order_callback(orderInfo)
    except Exception as e:
        print(f"任务队列订单回调处理失败：{str(e)}")

def task_queue_deal_callback(ContextInfo, dealInfo):
    """
    任务队列成交回调函数
    需要在iQuant的deal_callback中调用此函数
    """
    try:
        g_trade_task_callback_handler.handle_deal_callback(dealInfo)
    except Exception as e:
        print(f"任务队列成交回调处理失败：{str(e)}")

def task_queue_process_pending_tasks(ContextInfo):
    """
    处理待执行任务
    需要在handlebar_main中调用此函数
    """
    try:
        # 检查ContextInfo是否有效
        if ContextInfo is None:
            print("警告：ContextInfo为None，跳过任务队列处理")
            return

        # 检查是否为最后一根K线（实盘模式）或者是回测模式
        if hasattr(ContextInfo, 'is_last_bar') and callable(getattr(ContextInfo, 'is_last_bar')):
            if ContextInfo.is_last_bar() or is_backtest_mode(ContextInfo):
                g_trade_task_executor.process_pending_tasks(ContextInfo)
        else:
            # 如果没有is_last_bar方法，直接处理（可能是测试环境）
            g_trade_task_executor.process_pending_tasks(ContextInfo)
    except Exception as e:
        print(f"处理任务队列失败：{str(e)}")
        log_message("ERROR", "任务队列", f"处理任务队列失败：{str(e)}", None, ContextInfo)


# ==================== iQuant 标准回调函数 ====================

def order_callback(ContextInfo, orderInfo):
    """
    iQuant 订单回调函数
    当订单状态发生变化时，iQuant 会自动调用此函数

    Args:
        ContextInfo: iQuant上下文信息对象
        orderInfo: 订单信息对象
    """
    try:
        # 调用任务队列的订单回调处理
        task_queue_order_callback(ContextInfo, orderInfo)

        # 这里可以添加其他订单回调处理逻辑
        # 例如：记录订单日志、发送通知等

    except Exception as e:
        error_msg = f"订单回调处理失败：{str(e)}"
        print(f"❌ {error_msg}")
        log_message("ERROR", "订单回调", error_msg, None, ContextInfo)


def deal_callback(ContextInfo, dealInfo):
    """
    iQuant 成交回调函数
    当订单成交时，iQuant 会自动调用此函数

    Args:
        ContextInfo: iQuant上下文信息对象
        dealInfo: 成交信息对象
    """
    try:
        # 调用任务队列的成交回调处理
        task_queue_deal_callback(ContextInfo, dealInfo)

        # 这里可以添加其他成交回调处理逻辑
        # 例如：记录成交日志、更新持仓信息、发送通知等

    except Exception as e:
        error_msg = f"成交回调处理失败：{str(e)}"
        print(f"❌ {error_msg}")
        log_message("ERROR", "成交回调", error_msg, None, ContextInfo)


def orderError_callback(ContextInfo, orderArgs, errMsg):
    """
    iQuant 下单错误回调函数
    当下单错误时，iQuant 会自动调用此函数

    Args:
        ContextInfo: iQuant上下文信息对象
        orderArgs: 下单参数
        errMsg: 错误信息
    """
    try:
        # print('【下单错误回调】下单出错！！！')

        # # 打印所有订单参数用于调试
        # order_info = {}
        # for attr in dir(orderArgs):
        #     if not attr.startswith('_'):
        #         try:
        #             value = getattr(orderArgs, attr)
        #             if not callable(value):
        #                 order_info[attr] = value
        #                 print(f"   {attr} = {value}")
        #         except:
        #             pass

        print(f"【下单错误回调】错误信息：{errMsg}")

        # 提取订单UUID
        strategy_name = getattr(orderArgs, 'stragegyName', '')
        if '_&&&_' in strategy_name:
            # 格式：策略名_&&&_投资备注（UUID）
            order_uuid = strategy_name.split('_&&&_')[1]
        else:
            # 整个字段就是UUID
            order_uuid = strategy_name

        if not order_uuid:
            print("【下单错误回调】警告：无法提取订单UUID，跳过状态更新")
            return

        print(f"【下单错误回调】提取到订单UUID：{order_uuid}")

        # 更新数据库状态
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 1. 先从 trade_task_queue 表获取相关信息
        cursor.execute("""
            SELECT id, task_group_id, order_id FROM trade_task_queue
            WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
        """, (order_uuid,))

        task_info = cursor.fetchone()
        if not task_info:
            print(f"【下单错误回调】警告：未找到对应的任务记录，UUID={order_uuid}")
            return

        task_id, task_group_id, order_id = task_info
        print(f"【下单错误回调】找到任务记录：task_id={task_id}, order_id={order_id}")

        # 2. 更新 trade_task_queue 表状态
        cursor.execute("""
            UPDATE trade_task_queue
            SET task_status = 'FAILED',
                error_message = ?
            WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
        """, (str(errMsg), order_uuid))

        task_queue_updated = cursor.rowcount
        print(f"【下单错误回调】更新 trade_task_queue 表：{task_queue_updated} 条记录")

        # 3. 更新 trade_orders 表状态（现在可以直接通过 order_uuid 匹配）
        cursor.execute("""
            UPDATE trade_orders
            SET order_status = 'FAILED',
                error_message = ?
            WHERE order_uuid = ?
        """, (str(errMsg), order_uuid))

        trade_orders_updated = cursor.rowcount
        print(f"【下单错误回调】更新 trade_orders 表：{trade_orders_updated} 条记录")

        # 3. 记录错误日志到 trade_task_log
        cursor.execute("""
            SELECT task_id, task_group_id FROM trade_task_queue
            WHERE order_uuid = ?
        """, (order_uuid,))

        task_info = cursor.fetchone()
        if task_info:
            task_id, task_group_id = task_info

            cursor.execute("""
                INSERT INTO trade_task_log
                (task_id, task_group_id, log_level, log_category, log_message, log_time)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (task_id, task_group_id, "ERROR", "ORDER_ERROR",
                  f"下单失败：{errMsg}", current_time))

            print(f"【下单错误回调】记录错误日志：任务ID={task_id}，任务组ID={task_group_id}")

        # 提交数据库更改
        g_db_connection.commit()

        # 记录到系统日志
        log_message("ERROR", "下单错误",
                   f"订单UUID={order_uuid}，错误信息={errMsg}，已更新相关表状态",
                   None, ContextInfo)

        print(f"【下单错误回调】处理完成：UUID={order_uuid}")

    except Exception as e:
        error_msg = f"下单错误回调处理失败：{str(e)}"
        print(f"❌ {error_msg}")
        log_message("ERROR", "下单错误回调", error_msg, None, ContextInfo)
        import traceback
        traceback.print_exc()


def task_callback(ContextInfo, taskInfo):
    """
    iQuant 账号任务状态变化回调函数

    Args:
        ContextInfo: iQuant上下文信息对象
        taskInfo: 任务信息
    """
    # print('【shaojiahao0819】账号任务状态变化！！！')
    # for attr in dir(taskInfo):
    #     if not attr.startswith('_'):
    #         try:
    #             value = getattr(taskInfo, attr)
    #             if not callable(value):
    #                 print(f"   {attr} = {value}")
    #         except:
    #             pass
    pass
