# -*- coding: utf-8 -*-
"""
测试强制激活模式的修改
验证是否正确使用get_historical_highest_price方法
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略模块
from value_averaging_strategy import (
    get_historical_highest_price,
    force_activate_strategy,
    ACTIVE_FUND_CODE,
    FORCE_ACTIVE_MODE,
    AUTO_CALCULATE_ACTIVATION
)

def test_get_historical_highest_price():
    """测试get_historical_highest_price方法"""
    print("=" * 60)
    print("测试 get_historical_highest_price 方法")
    print("=" * 60)
    
    try:
        # 模拟ContextInfo对象
        class MockContextInfo:
            def get_market_data_ex(self, **kwargs):
                # 返回模拟数据
                return None
        
        mock_context = MockContextInfo()
        
        # 测试方法调用
        result = get_historical_highest_price(ACTIVE_FUND_CODE, 5, mock_context)
        print(f"结果: {result}")
        print(f"类型: {type(result)}")
        
        if isinstance(result, tuple) and len(result) == 2:
            date_str, price = result
            print(f"日期: {date_str}")
            print(f"价格: {price}")
            print("✓ 方法返回格式正确")
        else:
            print("✗ 方法返回格式错误")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")

def test_force_activation_config():
    """测试强制激活配置"""
    print("\n" + "=" * 60)
    print("测试强制激活配置")
    print("=" * 60)
    
    print(f"FORCE_ACTIVE_MODE: {FORCE_ACTIVE_MODE}")
    print(f"AUTO_CALCULATE_ACTIVATION: {AUTO_CALCULATE_ACTIVATION}")
    print(f"ACTIVE_FUND_CODE: {ACTIVE_FUND_CODE}")
    
    if FORCE_ACTIVE_MODE:
        print("✓ 强制激活模式已开启")
    else:
        print("✗ 强制激活模式未开启")

def print_code_changes():
    """打印代码修改说明"""
    print("\n" + "=" * 60)
    print("代码修改说明")
    print("=" * 60)
    
    changes = [
        "1. 修改了 force_activate_strategy 方法",
        "   - 在设置策略状态为激活期时，使用 get_historical_highest_price 方法",
        "   - 计算5年内最高收盘价作为价值平均起始期信息",
        "   - 添加了详细的 trade_log 日志记录",
        "",
        "2. 修改了阶段切换逻辑（回测模式和实盘模式）",
        "   - 在从沉睡期切换到激活期时添加 trade_log 日志",
        "   - 记录5年最高价的计算结果",
        "",
        "3. 修改了价值平均策略执行逻辑",
        "   - 在执行买入/卖出操作时添加 trade_log 日志",
        "   - 在无需调整时也添加 trade_log 日志",
        "   - 记录详细的期数、目标价值、当前价值等信息",
        "",
        "4. 关键修改点：",
        "   - 强制激活模式现在正确使用5年内最高收盘价作为起始期",
        "   - 而不是使用最近进入激活期点位的日期和价格",
        "   - 添加了完整的 trade_log 日志追踪"
    ]
    
    for change in changes:
        print(change)

if __name__ == "__main__":
    print("强制激活模式修改测试")
    print("=" * 60)
    
    # 测试配置
    test_force_activation_config()
    
    # 测试方法
    test_get_historical_highest_price()
    
    # 打印修改说明
    print_code_changes()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
